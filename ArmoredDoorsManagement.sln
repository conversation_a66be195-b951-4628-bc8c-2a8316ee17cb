﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.0.31903.59
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "src", "src", "{827E0CD3-B72D-47B6-A68D-7590B98EB39B}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Backend", "Backend", "{FE360695-3F2B-1049-3887-35FBB5135923}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.API", "src\Backend\ArmoredDoors.API\ArmoredDoors.API.csproj", "{571BF243-8B71-4463-B37C-5BCE85C532A9}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.Core", "src\Backend\ArmoredDoors.Core\ArmoredDoors.Core.csproj", "{20D49838-8220-44EF-AFB1-E454250CAF34}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.Infrastructure", "src\Backend\ArmoredDoors.Infrastructure\ArmoredDoors.Infrastructure.csproj", "{269700D4-85F3-45B6-BF83-DE1B86E31AE5}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.Application", "src\Backend\ArmoredDoors.Application\ArmoredDoors.Application.csproj", "{099FB52A-52B2-4015-8C6C-D90E321DF722}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.Shared", "src\Backend\ArmoredDoors.Shared\ArmoredDoors.Shared.csproj", "{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Tests", "Tests", "{CEA09484-30F6-4D44-02F6-822E06DBC57C}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.UnitTests", "src\Tests\ArmoredDoors.UnitTests\ArmoredDoors.UnitTests.csproj", "{C33B77E0-5254-41AA-90C6-662B083D2078}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "ArmoredDoors.IntegrationTests", "src\Tests\ArmoredDoors.IntegrationTests\ArmoredDoors.IntegrationTests.csproj", "{C075453C-B0BC-474C-A585-C157EACAE1BA}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|x64 = Debug|x64
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|x64 = Release|x64
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Debug|x64.ActiveCfg = Debug|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Debug|x64.Build.0 = Debug|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Debug|x86.ActiveCfg = Debug|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Debug|x86.Build.0 = Debug|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Release|Any CPU.Build.0 = Release|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Release|x64.ActiveCfg = Release|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Release|x64.Build.0 = Release|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Release|x86.ActiveCfg = Release|Any CPU
		{571BF243-8B71-4463-B37C-5BCE85C532A9}.Release|x86.Build.0 = Release|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Debug|x64.ActiveCfg = Debug|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Debug|x64.Build.0 = Debug|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Debug|x86.ActiveCfg = Debug|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Debug|x86.Build.0 = Debug|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Release|Any CPU.Build.0 = Release|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Release|x64.ActiveCfg = Release|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Release|x64.Build.0 = Release|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Release|x86.ActiveCfg = Release|Any CPU
		{20D49838-8220-44EF-AFB1-E454250CAF34}.Release|x86.Build.0 = Release|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Debug|x64.ActiveCfg = Debug|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Debug|x64.Build.0 = Debug|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Debug|x86.ActiveCfg = Debug|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Debug|x86.Build.0 = Debug|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Release|Any CPU.Build.0 = Release|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Release|x64.ActiveCfg = Release|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Release|x64.Build.0 = Release|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Release|x86.ActiveCfg = Release|Any CPU
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5}.Release|x86.Build.0 = Release|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Debug|x64.ActiveCfg = Debug|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Debug|x64.Build.0 = Debug|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Debug|x86.ActiveCfg = Debug|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Debug|x86.Build.0 = Debug|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Release|Any CPU.Build.0 = Release|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Release|x64.ActiveCfg = Release|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Release|x64.Build.0 = Release|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Release|x86.ActiveCfg = Release|Any CPU
		{099FB52A-52B2-4015-8C6C-D90E321DF722}.Release|x86.Build.0 = Release|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Debug|x64.ActiveCfg = Debug|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Debug|x64.Build.0 = Debug|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Debug|x86.ActiveCfg = Debug|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Debug|x86.Build.0 = Debug|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Release|Any CPU.Build.0 = Release|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Release|x64.ActiveCfg = Release|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Release|x64.Build.0 = Release|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Release|x86.ActiveCfg = Release|Any CPU
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE}.Release|x86.Build.0 = Release|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Debug|x64.Build.0 = Debug|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Debug|x86.Build.0 = Debug|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Release|Any CPU.Build.0 = Release|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Release|x64.ActiveCfg = Release|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Release|x64.Build.0 = Release|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Release|x86.ActiveCfg = Release|Any CPU
		{C33B77E0-5254-41AA-90C6-662B083D2078}.Release|x86.Build.0 = Release|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Debug|x64.ActiveCfg = Debug|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Debug|x64.Build.0 = Debug|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Debug|x86.Build.0 = Debug|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Release|Any CPU.Build.0 = Release|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Release|x64.ActiveCfg = Release|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Release|x64.Build.0 = Release|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Release|x86.ActiveCfg = Release|Any CPU
		{C075453C-B0BC-474C-A585-C157EACAE1BA}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{FE360695-3F2B-1049-3887-35FBB5135923} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{571BF243-8B71-4463-B37C-5BCE85C532A9} = {FE360695-3F2B-1049-3887-35FBB5135923}
		{20D49838-8220-44EF-AFB1-E454250CAF34} = {FE360695-3F2B-1049-3887-35FBB5135923}
		{269700D4-85F3-45B6-BF83-DE1B86E31AE5} = {FE360695-3F2B-1049-3887-35FBB5135923}
		{099FB52A-52B2-4015-8C6C-D90E321DF722} = {FE360695-3F2B-1049-3887-35FBB5135923}
		{43DD992C-4A4D-42CE-9FF4-B955508DD9AE} = {FE360695-3F2B-1049-3887-35FBB5135923}
		{CEA09484-30F6-4D44-02F6-822E06DBC57C} = {827E0CD3-B72D-47B6-A68D-7590B98EB39B}
		{C33B77E0-5254-41AA-90C6-662B083D2078} = {CEA09484-30F6-4D44-02F6-822E06DBC57C}
		{C075453C-B0BC-474C-A585-C157EACAE1BA} = {CEA09484-30F6-4D44-02F6-822E06DBC57C}
	EndGlobalSection
EndGlobal
