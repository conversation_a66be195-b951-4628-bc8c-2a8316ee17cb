// Dashboard Styles
.dashboard {
  @apply space-y-6;
}

// Dashboard Header
.dashboard-header {
  @apply mb-8;

  .dashboard-title {
    @apply text-3xl font-bold text-gray-900 mb-2;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-2xl;
    }
  }

  .dashboard-subtitle {
    @apply text-gray-600;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-sm;
    }
  }
}

// Stats Grid
.stats-grid {
  @apply grid gap-6 mb-8;

  // Responsive grid
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));

  // Mobile: Single column
  @media (max-width: 767px) {
    @apply grid-cols-1 gap-4;
  }

  // Tablet: Two columns
  @media (min-width: 768px) and (max-width: 1023px) {
    @apply grid-cols-2;
  }

  // Desktop: Four columns
  @media (min-width: 1024px) {
    @apply grid-cols-4;
  }
}

.stat-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow duration-200;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply p-4;
  }

  // Flex layout
  @apply flex items-center space-x-4;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }
}

.stat-icon {
  @apply w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply w-10 h-10;
  }
}

.stat-content {
  @apply flex-1;

  .stat-value {
    @apply text-2xl font-bold text-gray-900 mb-1;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-xl;
    }
  }

  .stat-label {
    @apply text-sm text-gray-600;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-xs;
    }
  }
}

// Dashboard Content
.dashboard-content {
  @apply space-y-6;
}

.content-grid {
  @apply grid gap-6;

  // Responsive grid
  @media (max-width: 1023px) {
    @apply grid-cols-1;
  }

  @media (min-width: 1024px) {
    @apply grid-cols-2;
  }
}

.content-card {
  @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
}

.card-header {
  @apply flex items-center justify-between p-6 border-b border-gray-200 bg-gray-50;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply p-4 flex-col space-y-3 items-start;
  }

  .card-title {
    @apply text-lg font-semibold text-gray-900;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-base;
    }
  }
}

.card-content {
  @apply p-6 space-y-4;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply p-4 space-y-3;
  }
}

// Order Items
.order-item {
  @apply flex items-center justify-between p-4 bg-gray-50 rounded-lg;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply flex-col items-start space-y-2 p-3;
  }
}

.order-info {
  @apply flex-1;

  .order-title {
    @apply font-medium text-gray-900 mb-1;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-sm;
    }
  }

  .order-customer {
    @apply text-sm text-gray-600;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-xs;
    }
  }
}

.order-status {
  @apply flex-shrink-0;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply self-end;
  }
}

// Status Badges
.status-badge {
  @apply px-3 py-1 text-xs font-medium rounded-full;

  &.status-pending {
    @apply bg-yellow-100 text-yellow-800;
  }

  &.status-completed {
    @apply bg-green-100 text-green-800;
  }

  &.status-cancelled {
    @apply bg-red-100 text-red-800;
  }
}

// Alert Items
.alert-item {
  @apply flex items-start space-x-3 p-4 bg-gray-50 rounded-lg;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply p-3 space-x-2;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }
  }
}

.alert-icon {
  @apply flex-shrink-0 mt-0.5;
}

.alert-content {
  @apply flex-1;

  .alert-title {
    @apply font-medium text-gray-900 mb-1;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-sm;
    }
  }

  .alert-description {
    @apply text-sm text-gray-600;

    // Mobile adjustments
    @media (max-width: 767px) {
      @apply text-xs;
    }
  }
}

// Button Styles
.btn-secondary {
  @apply px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-lg hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50 transition-colors duration-200;

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply px-3 py-1 text-xs;
  }
}

// Animation classes
.fade-in {
  @apply animate-fade-in;
}

.slide-up {
  animation: slideUp 0.5s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .stat-card,
  .btn-secondary {
    @apply transition-none;
  }

  .slide-up {
    animation: none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .stat-card,
  .content-card {
    @apply border-2 border-gray-900;
  }

  .status-badge {
    @apply border border-gray-900;
  }
}