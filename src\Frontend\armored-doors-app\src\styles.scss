/* Tailwind CSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts for Arabic and English */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* Angular Material Theme */
@import '~@angular/material/theming';

/* Toastr Styles */
@import 'ngx-toastr/toastr';

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply bg-gray-50 text-gray-900 antialiased;
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
  }

  /* Arabic font family */
  [dir="rtl"] body,
  .arabic {
    font-family: 'Cairo', 'Tajawal', sans-serif;
  }

  /* English font family */
  [dir="ltr"] body,
  .english {
    font-family: 'Inter', system-ui, sans-serif;
  }

  /* RTL Support */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="ltr"] {
    text-align: left;
  }

  /* Custom scrollbar */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400;
  }

  /* Focus styles */
  *:focus {
    outline: none;
    @apply ring-2 ring-primary-500 ring-opacity-50;
  }

  /* Selection styles */
  ::selection {
    @apply bg-primary-100 text-primary-900;
  }
}

/* Component styles */
@layer components {
  /* Button variants */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply bg-secondary-600 text-white hover:bg-secondary-700 focus:ring-secondary-500;
  }

  .btn-success {
    @apply bg-success-600 text-white hover:bg-success-700 focus:ring-success-500;
  }

  .btn-warning {
    @apply bg-warning-600 text-white hover:bg-warning-700 focus:ring-warning-500;
  }

  .btn-danger {
    @apply bg-danger-600 text-white hover:bg-danger-700 focus:ring-danger-500;
  }

  .btn-outline {
    @apply border border-gray-300 bg-white text-gray-700 hover:bg-gray-50 focus:ring-gray-500;
  }

  /* Card styles */
  .card {
    @apply bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 bg-gray-50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 bg-gray-50;
  }

  /* Form styles */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 mb-2;
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-lg shadow-sm focus:ring-primary-500 focus:border-primary-500 transition-colors duration-200;
  }

  .form-error {
    @apply text-sm text-danger-600 mt-1;
  }

  /* Table styles */
  .table {
    @apply w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-body td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
}

/* Utility classes */
@layer utilities {
  /* RTL utilities */
  .rtl-flip {
    transform: scaleX(-1);
  }

  [dir="rtl"] .rtl-flip {
    transform: scaleX(1);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  /* Text direction utilities */
  .text-start {
    text-align: start;
  }

  .text-end {
    text-align: end;
  }

  /* Margin and padding RTL utilities */
  [dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  [dir="rtl"] .mr-auto {
    margin-right: 0;
    margin-left: auto;
  }

  [dir="rtl"] .pl-4 {
    padding-left: 0;
    padding-right: 1rem;
  }

  [dir="rtl"] .pr-4 {
    padding-right: 0;
    padding-left: 1rem;
  }
}

/* Angular Material customizations */
.mat-toolbar {
  @apply bg-white border-b border-gray-200;
}

.mat-sidenav {
  @apply border-r border-gray-200;
}

[dir="rtl"] .mat-sidenav {
  @apply border-r-0 border-l border-gray-200;
}

/* Toastr customizations */
.toast-container {
  @apply z-50;
}

.toast-success {
  @apply bg-success-600;
}

.toast-error {
  @apply bg-danger-600;
}

.toast-warning {
  @apply bg-warning-600;
}

.toast-info {
  @apply bg-primary-600;
}
