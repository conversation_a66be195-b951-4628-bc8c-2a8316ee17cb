/* Tailwind CSS imports */
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Google Fonts for Arabic and English */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;500;600;700&display=swap');
@import url('https://fonts.googleapis.com/css2?family=Tajawal:wght@300;400;500;700&display=swap');

/* Toastr Styles */


/* CSS Variables for theming */
:root {
  /* Light theme colors */
  --bg-primary: #ffffff;
  --bg-secondary: #f9fafb;
  --bg-tertiary: #f3f4f6;
  --text-primary: #111827;
  --text-secondary: #6b7280;
  --text-tertiary: #9ca3af;
  --border-color: #e5e7eb;
  --border-hover: #d1d5db;

  /* Arabic font optimization */
  --arabic-font-weight: 500;
  --arabic-line-height: 1.7;
  --arabic-letter-spacing: 0.025em;
  --arabic-font-size-multiplier: 1.05;
}

/* Dark theme colors */
.dark {
  --bg-primary: #111827;
  --bg-secondary: #1f2937;
  --bg-tertiary: #374151;
  --text-primary: #f9fafb;
  --text-secondary: #d1d5db;
  --text-tertiary: #9ca3af;
  --border-color: #374151;
  --border-hover: #4b5563;
}

/* Base styles */
@layer base {
  html {
    scroll-behavior: smooth;
    @apply transition-colors duration-300;
  }

  body {
    @apply bg-gray-50 dark:bg-gray-900 text-gray-900 dark:text-gray-100 antialiased transition-colors duration-300;
    font-family: 'Inter', system-ui, sans-serif;
    line-height: 1.6;
  }

  /* Arabic font family with optimizations */
  [dir="rtl"] body,
  .arabic {
    font-family: 'Cairo', 'Tajawal', sans-serif;
    font-weight: var(--arabic-font-weight);
    line-height: var(--arabic-line-height);
    letter-spacing: var(--arabic-letter-spacing);
  }

  /* Enhanced Arabic text styling */
  [dir="rtl"] h1, [dir="rtl"] h2, [dir="rtl"] h3, [dir="rtl"] h4, [dir="rtl"] h5, [dir="rtl"] h6,
  .arabic h1, .arabic h2, .arabic h3, .arabic h4, .arabic h5, .arabic h6 {
    font-weight: 600;
    line-height: 1.4;
  }

  [dir="rtl"] p, [dir="rtl"] span, [dir="rtl"] div,
  .arabic p, .arabic span, .arabic div {
    font-size: calc(1em * var(--arabic-font-size-multiplier));
  }

  /* English font family */
  [dir="ltr"] body,
  .english {
    font-family: 'Inter', system-ui, sans-serif;
  }

  /* RTL Support */
  [dir="rtl"] {
    text-align: right;
  }

  [dir="ltr"] {
    text-align: left;
  }

  /* Custom scrollbar with dark mode support */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-gray-100 dark:bg-gray-800;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-gray-300 dark:bg-gray-600 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-gray-400 dark:bg-gray-500;
  }

  /* Focus styles with dark mode support */
  *:focus {
    outline: none;
    @apply ring-2 ring-primary-500 dark:ring-primary-400 ring-opacity-50;
  }

  /* Selection styles with dark mode support */
  ::selection {
    @apply bg-primary-100 dark:bg-primary-800 text-primary-900 dark:text-primary-100;
  }
}

/* Component styles */
@layer components {
  /* Button variants with dark mode support */
  .btn {
    @apply inline-flex items-center justify-center px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2 dark:focus:ring-offset-gray-800 disabled:opacity-50 disabled:cursor-not-allowed;
  }

  .btn-primary {
    @apply bg-primary-600 text-white hover:bg-primary-700 dark:bg-primary-500 dark:hover:bg-primary-600 focus:ring-primary-500 dark:focus:ring-primary-400;
  }

  .btn-secondary {
    @apply bg-gray-600 text-white hover:bg-gray-700 dark:bg-gray-500 dark:hover:bg-gray-600 focus:ring-gray-500 dark:focus:ring-gray-400;
  }

  .btn-success {
    @apply bg-green-600 text-white hover:bg-green-700 dark:bg-green-500 dark:hover:bg-green-600 focus:ring-green-500 dark:focus:ring-green-400;
  }

  .btn-warning {
    @apply bg-yellow-600 text-white hover:bg-yellow-700 dark:bg-yellow-500 dark:hover:bg-yellow-600 focus:ring-yellow-500 dark:focus:ring-yellow-400;
  }

  .btn-danger {
    @apply bg-red-600 text-white hover:bg-red-700 dark:bg-red-500 dark:hover:bg-red-600 focus:ring-red-500 dark:focus:ring-red-400;
  }

  .btn-outline {
    @apply border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-200 hover:bg-gray-50 dark:hover:bg-gray-700 focus:ring-gray-500 dark:focus:ring-gray-400;
  }

  /* Card styles with dark mode support */
  .card {
    @apply bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 overflow-hidden transition-colors duration-200;
  }

  .card-header {
    @apply px-6 py-4 border-b border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50;
  }

  .card-body {
    @apply px-6 py-4;
  }

  .card-footer {
    @apply px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-900/50;
  }

  /* Form styles with dark mode support */
  .form-group {
    @apply mb-4;
  }

  .form-label {
    @apply block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2;

    /* Arabic label optimization */
    [dir="rtl"] & {
      font-weight: 600;
    }
  }

  .form-input {
    @apply w-full px-3 py-2 border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-900 dark:text-gray-100 rounded-lg shadow-sm focus:ring-primary-500 dark:focus:ring-primary-400 focus:border-primary-500 dark:focus:border-primary-400 transition-colors duration-200;

    /* Arabic input optimization */
    [dir="rtl"] & {
      font-weight: 500;
    }

    &::placeholder {
      @apply text-gray-400 dark:text-gray-500;
    }
  }

  .form-error {
    @apply text-sm text-red-600 dark:text-red-400 mt-1;
  }

  /* Table styles */
  .table {
    @apply w-full divide-y divide-gray-200;
  }

  .table-header {
    @apply bg-gray-50;
  }

  .table-header th {
    @apply px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider;
  }

  .table-body {
    @apply bg-white divide-y divide-gray-200;
  }

  .table-body td {
    @apply px-6 py-4 whitespace-nowrap text-sm text-gray-900;
  }

  /* Loading spinner */
  .spinner {
    @apply inline-block w-4 h-4 border-2 border-current border-t-transparent rounded-full animate-spin;
  }

  /* Badge styles */
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }

  .badge-primary {
    @apply bg-primary-100 text-primary-800;
  }

  .badge-success {
    @apply bg-success-100 text-success-800;
  }

  .badge-warning {
    @apply bg-warning-100 text-warning-800;
  }

  .badge-danger {
    @apply bg-danger-100 text-danger-800;
  }
}

/* Utility classes */
@layer utilities {
  /* RTL utilities */
  .rtl-flip {
    transform: scaleX(-1);
  }

  [dir="rtl"] .rtl-flip {
    transform: scaleX(1);
  }

  /* Animation utilities */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.3s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.3s ease-out;
  }

  /* Text direction utilities */
  .text-start {
    text-align: start;
  }

  .text-end {
    text-align: end;
  }

  /* Margin and padding RTL utilities */
  [dir="rtl"] .ml-auto {
    margin-left: 0;
    margin-right: auto;
  }

  [dir="rtl"] .mr-auto {
    margin-right: 0;
    margin-left: auto;
  }

  [dir="rtl"] .pl-4 {
    padding-left: 0;
    padding-right: 1rem;
  }

  [dir="rtl"] .pr-4 {
    padding-right: 0;
    padding-left: 1rem;
  }
}

/* Angular Material customizations */
.mat-toolbar {
  @apply bg-white border-b border-gray-200;
}

.mat-sidenav {
  @apply border-r border-gray-200;
}

[dir="rtl"] .mat-sidenav {
  @apply border-r-0 border-l border-gray-200;
}

/* Toastr customizations */
.toast-container {
  @apply z-50;
}

.toast-success {
  @apply bg-success-600;
}

.toast-error {
  @apply bg-danger-600;
}

.toast-warning {
  @apply bg-warning-600;
}

.toast-info {
  @apply bg-primary-600;
}
