<div class="min-h-screen flex items-center justify-center bg-gray-50 py-12 px-4 sm:px-6 lg:px-8" [attr.dir]="currentLanguage.direction">
  <div class="max-w-md w-full space-y-8">
    <!-- Language Switcher -->
    <div class="flex justify-end">
      <app-language-switcher></app-language-switcher>
    </div>

    <!-- Header -->
    <div class="text-center">
      <div class="mx-auto h-16 w-16 bg-primary-600 rounded-full flex items-center justify-center mb-6">
        <svg class="h-8 w-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 15v2m-6 4h12a2 2 0 002-2v-6a2 2 0 00-2-2H6a2 2 0 00-2 2v6a2 2 0 002 2zm10-10V7a4 4 0 00-8 0v4h8z"></path>
        </svg>
      </div>
      <h2 class="text-3xl font-bold text-gray-900 mb-2">
        {{ isRTL ? 'تسجيل الدخول' : 'Sign In' }}
      </h2>
      <p class="text-sm text-gray-600">
        {{ isRTL ? 'قم بتسجيل الدخول إلى حسابك للمتابعة' : 'Sign in to your account to continue' }}
      </p>
    </div>

    <!-- Login Form -->
    <form class="mt-8 space-y-6" [formGroup]="loginForm" (ngSubmit)="onSubmit()">
      <div class="space-y-4">
        <!-- Username Field -->
        <div>
          <label for="username" class="block text-sm font-medium text-gray-700 mb-2">
            {{ isRTL ? 'اسم المستخدم' : 'Username' }}
          </label>
          <div class="relative">
            <input
              id="username"
              name="username"
              type="text"
              formControlName="username"
              class="form-input"
              [class.border-red-500]="isFieldInvalid('username')"
              [class.focus:ring-red-500]="isFieldInvalid('username')"
              [class.focus:border-red-500]="isFieldInvalid('username')"
              [placeholder]="isRTL ? 'أدخل اسم المستخدم' : 'Enter your username'"
              [attr.dir]="currentLanguage.direction"
            />
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none" *ngIf="!isRTL">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
            <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none" *ngIf="isRTL">
              <svg class="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
              </svg>
            </div>
          </div>
          <div *ngIf="isFieldInvalid('username')" class="form-error">
            {{ getFieldError('username') }}
          </div>
        </div>

        <!-- Password Field -->
        <div>
          <label for="password" class="block text-sm font-medium text-gray-700 mb-2">
            {{ isRTL ? 'كلمة المرور' : 'Password' }}
          </label>
          <div class="relative">
            <input
              id="password"
              name="password"
              [type]="showPassword ? 'text' : 'password'"
              formControlName="password"
              class="form-input pr-10"
              [class.border-red-500]="isFieldInvalid('password')"
              [class.focus:ring-red-500]="isFieldInvalid('password')"
              [class.focus:border-red-500]="isFieldInvalid('password')"
              [placeholder]="isRTL ? 'أدخل كلمة المرور' : 'Enter your password'"
              [attr.dir]="currentLanguage.direction"
            />
            <button
              type="button"
              class="absolute inset-y-0 right-0 pr-3 flex items-center"
              (click)="togglePasswordVisibility()"
            >
              <svg *ngIf="!showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"></path>
              </svg>
              <svg *ngIf="showPassword" class="h-5 w-5 text-gray-400 hover:text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.875 18.825A10.05 10.05 0 0112 19c-4.478 0-8.268-2.943-9.543-7a9.97 9.97 0 011.563-3.029m5.858.908a3 3 0 114.243 4.243M9.878 9.878l4.242 4.242M9.878 9.878L3 3m6.878 6.878L21 21"></path>
              </svg>
            </button>
          </div>
          <div *ngIf="isFieldInvalid('password')" class="form-error">
            {{ getFieldError('password') }}
          </div>
        </div>

        <!-- Remember Me -->
        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input
              id="rememberMe"
              name="rememberMe"
              type="checkbox"
              formControlName="rememberMe"
              class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-gray-300 rounded"
            />
            <label for="rememberMe" class="ml-2 block text-sm text-gray-700">
              {{ isRTL ? 'تذكرني' : 'Remember me' }}
            </label>
          </div>
          <div class="text-sm">
            <a href="#" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200">
              {{ isRTL ? 'نسيت كلمة المرور؟' : 'Forgot your password?' }}
            </a>
          </div>
        </div>
      </div>

      <!-- Submit Button -->
      <div>
        <button
          type="submit"
          [disabled]="isLoading || loginForm.invalid"
          class="btn btn-primary w-full flex justify-center items-center py-3 text-base font-medium disabled:opacity-50 disabled:cursor-not-allowed"
        >
          <div *ngIf="isLoading" class="spinner mr-2"></div>
          <span>{{ isRTL ? 'تسجيل الدخول' : 'Sign In' }}</span>
        </button>
      </div>

      <!-- Footer -->
      <div class="text-center">
        <p class="text-sm text-gray-600">
          {{ isRTL ? 'ليس لديك حساب؟' : "Don't have an account?" }}
          <a href="#" class="font-medium text-primary-600 hover:text-primary-500 transition-colors duration-200 ml-1">
            {{ isRTL ? 'إنشاء حساب جديد' : 'Sign up' }}
          </a>
        </p>
      </div>
    </form>
  </div>
</div>
