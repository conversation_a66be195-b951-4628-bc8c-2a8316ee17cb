# Database Schema Design

## Overview
This document outlines the comprehensive database schema for the Armored Doors Manufacturing Management System. The schema is designed to support multi-partner capital management, warehouse operations, financial tracking, and user management.

## Core Business Requirements
- Multi-partner company with capital share-based ownership
- Real-time profit distribution calculations
- Complete warehouse management for raw materials and finished products
- Financial management with customer/supplier accounts
- User management with role-based permissions
- Audit trail for all business transactions

## Database Tables Structure

### 1. User Management & Security

#### Users
```sql
CREATE TABLE Users (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Username NVARCHAR(50) NOT NULL UNIQUE,
    Email NVARCHAR(100) NOT NULL UNIQUE,
    PasswordHash NVARCHAR(255) NOT NULL,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    PhoneNumber NVARCHAR(20),
    IsActive BIT NOT NULL DEFAULT 1,
    LastLoginDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Users_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Users_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id)
);
```

#### Roles
```sql
CREATE TABLE Roles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER
);
```

#### UserRoles
```sql
CREATE TABLE UserRoles (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    UserId UNIQUEIDENTIFIER NOT NULL,
    RoleId UNIQUEIDENTIFIER NOT NULL,
    AssignedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    AssignedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_UserRoles_User FOREIGN KEY (UserId) REFERENCES Users(Id) ON DELETE CASCADE,
    CONSTRAINT FK_UserRoles_Role FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
    CONSTRAINT FK_UserRoles_AssignedBy FOREIGN KEY (AssignedBy) REFERENCES Users(Id),
    CONSTRAINT UQ_UserRoles_UserRole UNIQUE (UserId, RoleId)
);
```

#### Permissions
```sql
CREATE TABLE Permissions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL UNIQUE,
    Description NVARCHAR(255),
    Module NVARCHAR(50) NOT NULL,
    Action NVARCHAR(50) NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1
);
```

#### RolePermissions
```sql
CREATE TABLE RolePermissions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    RoleId UNIQUEIDENTIFIER NOT NULL,
    PermissionId UNIQUEIDENTIFIER NOT NULL,
    GrantedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    GrantedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_RolePermissions_Role FOREIGN KEY (RoleId) REFERENCES Roles(Id) ON DELETE CASCADE,
    CONSTRAINT FK_RolePermissions_Permission FOREIGN KEY (PermissionId) REFERENCES Permissions(Id) ON DELETE CASCADE,
    CONSTRAINT FK_RolePermissions_GrantedBy FOREIGN KEY (GrantedBy) REFERENCES Users(Id),
    CONSTRAINT UQ_RolePermissions_RolePermission UNIQUE (RoleId, PermissionId)
);
```

### 2. Partner & Capital Management

#### Partners
```sql
CREATE TABLE Partners (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    Email NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(500),
    NationalId NVARCHAR(20),
    TaxNumber NVARCHAR(20),
    InitialCapitalAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
    CurrentCapitalAmount DECIMAL(18,2) NOT NULL DEFAULT 0,
    CurrentOwnershipPercentage DECIMAL(5,4) NOT NULL DEFAULT 0,
    JoinDate DATE NOT NULL,
    IsActive BIT NOT NULL DEFAULT 1,
    Notes NVARCHAR(1000),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Partners_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Partners_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id)
);
```

#### CapitalTransactions
```sql
CREATE TABLE CapitalTransactions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PartnerId UNIQUEIDENTIFIER NOT NULL,
    TransactionType NVARCHAR(20) NOT NULL, -- 'Injection', 'Withdrawal'
    Amount DECIMAL(18,2) NOT NULL,
    TransactionDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    Description NVARCHAR(500),
    ReferenceNumber NVARCHAR(50),
    PreviousCapitalAmount DECIMAL(18,2) NOT NULL,
    NewCapitalAmount DECIMAL(18,2) NOT NULL,
    PreviousOwnershipPercentage DECIMAL(5,4) NOT NULL,
    NewOwnershipPercentage DECIMAL(5,4) NOT NULL,
    ApprovedBy UNIQUEIDENTIFIER,
    ApprovedDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT FK_CapitalTransactions_Partner FOREIGN KEY (PartnerId) REFERENCES Partners(Id),
    CONSTRAINT FK_CapitalTransactions_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Users(Id),
    CONSTRAINT FK_CapitalTransactions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_CapitalTransactions_Type CHECK (TransactionType IN ('Injection', 'Withdrawal')),
    CONSTRAINT CK_CapitalTransactions_Amount CHECK (Amount > 0)
);
```

#### ProfitDistributions
```sql
CREATE TABLE ProfitDistributions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    PartnerId UNIQUEIDENTIFIER NOT NULL,
    PeriodStartDate DATE NOT NULL,
    PeriodEndDate DATE NOT NULL,
    TotalCompanyProfit DECIMAL(18,2) NOT NULL,
    PartnerOwnershipPercentage DECIMAL(5,4) NOT NULL,
    PartnerProfitShare DECIMAL(18,2) NOT NULL,
    DistributionDate DATETIME2,
    Status NVARCHAR(20) NOT NULL DEFAULT 'Calculated', -- 'Calculated', 'Distributed', 'Cancelled'
    Notes NVARCHAR(500),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT FK_ProfitDistributions_Partner FOREIGN KEY (PartnerId) REFERENCES Partners(Id),
    CONSTRAINT FK_ProfitDistributions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_ProfitDistributions_Status CHECK (Status IN ('Calculated', 'Distributed', 'Cancelled'))
);
```

### 3. Warehouse Management - Lookup Tables

#### Categories
```sql
CREATE TABLE Categories (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    CategoryType NVARCHAR(20) NOT NULL, -- 'RawMaterial', 'FinishedProduct'
    ParentCategoryId UNIQUEIDENTIFIER,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Categories_Parent FOREIGN KEY (ParentCategoryId) REFERENCES Categories(Id),
    CONSTRAINT FK_Categories_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_Categories_Type CHECK (CategoryType IN ('RawMaterial', 'FinishedProduct'))
);
```

#### MeasurementUnits
```sql
CREATE TABLE MeasurementUnits (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Name NVARCHAR(50) NOT NULL UNIQUE,
    Symbol NVARCHAR(10) NOT NULL,
    UnitType NVARCHAR(20) NOT NULL, -- 'Weight', 'Count', 'Length', 'Volume', 'Area'
    BaseUnit NVARCHAR(50),
    ConversionFactor DECIMAL(18,6),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT CK_MeasurementUnits_Type CHECK (UnitType IN ('Weight', 'Count', 'Length', 'Volume', 'Area'))
);
```

#### StorageLocations
```sql
CREATE TABLE StorageLocations (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Code NVARCHAR(20) NOT NULL UNIQUE,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    LocationType NVARCHAR(20) NOT NULL, -- 'Warehouse', 'Section', 'Shelf', 'Bin'
    ParentLocationId UNIQUEIDENTIFIER,
    Capacity DECIMAL(18,2),
    CurrentOccupancy DECIMAL(18,2) DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_StorageLocations_Parent FOREIGN KEY (ParentLocationId) REFERENCES StorageLocations(Id),
    CONSTRAINT FK_StorageLocations_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_StorageLocations_Type CHECK (LocationType IN ('Warehouse', 'Section', 'Shelf', 'Bin'))
);
```

### 4. Raw Materials Management

#### RawMaterials
```sql
CREATE TABLE RawMaterials (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Code NVARCHAR(50) NOT NULL UNIQUE,
    Name NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    CategoryId UNIQUEIDENTIFIER NOT NULL,
    MeasurementUnitId UNIQUEIDENTIFIER NOT NULL,
    MinimumStockLevel DECIMAL(18,2) NOT NULL DEFAULT 0,
    MaximumStockLevel DECIMAL(18,2),
    ReorderPoint DECIMAL(18,2),
    StandardCost DECIMAL(18,2),
    CurrentStockQuantity DECIMAL(18,2) NOT NULL DEFAULT 0,
    CurrentStockValue DECIMAL(18,2) NOT NULL DEFAULT 0,
    DefaultStorageLocationId UNIQUEIDENTIFIER,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_RawMaterials_Category FOREIGN KEY (CategoryId) REFERENCES Categories(Id),
    CONSTRAINT FK_RawMaterials_MeasurementUnit FOREIGN KEY (MeasurementUnitId) REFERENCES MeasurementUnits(Id),
    CONSTRAINT FK_RawMaterials_StorageLocation FOREIGN KEY (DefaultStorageLocationId) REFERENCES StorageLocations(Id),
    CONSTRAINT FK_RawMaterials_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_RawMaterials_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id)
);
```

#### RawMaterialImages
```sql
CREATE TABLE RawMaterialImages (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    RawMaterialId UNIQUEIDENTIFIER NOT NULL,
    ImagePath NVARCHAR(500) NOT NULL,
    ImageName NVARCHAR(255) NOT NULL,
    ImageSize BIGINT,
    IsPrimary BIT NOT NULL DEFAULT 0,
    DisplayOrder INT NOT NULL DEFAULT 0,
    UploadedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UploadedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_RawMaterialImages_RawMaterial FOREIGN KEY (RawMaterialId) REFERENCES RawMaterials(Id) ON DELETE CASCADE,
    CONSTRAINT FK_RawMaterialImages_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Users(Id)
);
```

### 5. Finished Products Management

#### FinishedProducts
```sql
CREATE TABLE FinishedProducts (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Code NVARCHAR(50) NOT NULL UNIQUE,
    Name NVARCHAR(200) NOT NULL,
    Description NVARCHAR(500),
    CategoryId UNIQUEIDENTIFIER NOT NULL,
    MeasurementUnitId UNIQUEIDENTIFIER NOT NULL,
    Barcode NVARCHAR(50) UNIQUE,
    MinimumStockLevel DECIMAL(18,2) NOT NULL DEFAULT 0,
    MaximumStockLevel DECIMAL(18,2),
    ReorderPoint DECIMAL(18,2),
    StandardCost DECIMAL(18,2),
    SellingPrice DECIMAL(18,2),
    WholesalePrice DECIMAL(18,2),
    CurrentStockQuantity DECIMAL(18,2) NOT NULL DEFAULT 0,
    CurrentStockValue DECIMAL(18,2) NOT NULL DEFAULT 0,
    DefaultStorageLocationId UNIQUEIDENTIFIER,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_FinishedProducts_Category FOREIGN KEY (CategoryId) REFERENCES Categories(Id),
    CONSTRAINT FK_FinishedProducts_MeasurementUnit FOREIGN KEY (MeasurementUnitId) REFERENCES MeasurementUnits(Id),
    CONSTRAINT FK_FinishedProducts_StorageLocation FOREIGN KEY (DefaultStorageLocationId) REFERENCES StorageLocations(Id),
    CONSTRAINT FK_FinishedProducts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_FinishedProducts_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id)
);
```

#### FinishedProductImages
```sql
CREATE TABLE FinishedProductImages (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    FinishedProductId UNIQUEIDENTIFIER NOT NULL,
    ImagePath NVARCHAR(500) NOT NULL,
    ImageName NVARCHAR(255) NOT NULL,
    ImageSize BIGINT,
    IsPrimary BIT NOT NULL DEFAULT 0,
    DisplayOrder INT NOT NULL DEFAULT 0,
    UploadedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    UploadedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_FinishedProductImages_FinishedProduct FOREIGN KEY (FinishedProductId) REFERENCES FinishedProducts(Id) ON DELETE CASCADE,
    CONSTRAINT FK_FinishedProductImages_UploadedBy FOREIGN KEY (UploadedBy) REFERENCES Users(Id)
);
```

## Indexes and Performance Optimization

### Primary Indexes
- All tables have clustered indexes on their primary keys (UNIQUEIDENTIFIER)
- Unique constraints on business keys (codes, usernames, emails)

### Secondary Indexes
```sql
-- User Management
CREATE INDEX IX_Users_Username ON Users(Username);
CREATE INDEX IX_Users_Email ON Users(Email);
CREATE INDEX IX_Users_IsActive ON Users(IsActive);

-- Partners
CREATE INDEX IX_Partners_Name ON Partners(Name);
CREATE INDEX IX_Partners_IsActive ON Partners(IsActive);

-- Capital Transactions
CREATE INDEX IX_CapitalTransactions_Partner_Date ON CapitalTransactions(PartnerId, TransactionDate);
CREATE INDEX IX_CapitalTransactions_Type ON CapitalTransactions(TransactionType);

-- Raw Materials
CREATE INDEX IX_RawMaterials_Code ON RawMaterials(Code);
CREATE INDEX IX_RawMaterials_Name ON RawMaterials(Name);
CREATE INDEX IX_RawMaterials_Category ON RawMaterials(CategoryId);
CREATE INDEX IX_RawMaterials_IsActive ON RawMaterials(IsActive);

-- Finished Products
CREATE INDEX IX_FinishedProducts_Code ON FinishedProducts(Code);
CREATE INDEX IX_FinishedProducts_Name ON FinishedProducts(Name);
CREATE INDEX IX_FinishedProducts_Barcode ON FinishedProducts(Barcode);
CREATE INDEX IX_FinishedProducts_Category ON FinishedProducts(CategoryId);
CREATE INDEX IX_FinishedProducts_IsActive ON FinishedProducts(IsActive);
```

## Data Integrity and Constraints

### Business Rules Enforced at Database Level
1. **Capital Transactions**: Amount must be positive
2. **Ownership Percentages**: Must be between 0 and 1 (0% to 100%)
3. **Stock Quantities**: Cannot be negative
4. **Email Uniqueness**: Enforced across users and partners
5. **Soft Deletes**: Using IsActive flags instead of hard deletes

### Audit Trail
- All major tables include CreatedDate, CreatedBy, ModifiedDate, ModifiedBy
- Capital transactions maintain previous and new values for audit purposes
- User activity logging through separate audit tables (to be implemented)

### 6. Inventory Movements

#### InventoryMovements
```sql
CREATE TABLE InventoryMovements (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    MovementType NVARCHAR(20) NOT NULL, -- 'Inbound', 'Outbound', 'Transfer', 'Adjustment'
    ItemType NVARCHAR(20) NOT NULL, -- 'RawMaterial', 'FinishedProduct'
    ItemId UNIQUEIDENTIFIER NOT NULL,
    Quantity DECIMAL(18,2) NOT NULL,
    UnitCost DECIMAL(18,2),
    TotalCost DECIMAL(18,2),
    FromLocationId UNIQUEIDENTIFIER,
    ToLocationId UNIQUEIDENTIFIER,
    MovementDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    ReferenceType NVARCHAR(50), -- 'Purchase', 'Sale', 'Production', 'Transfer', 'Adjustment'
    ReferenceId UNIQUEIDENTIFIER,
    ReferenceNumber NVARCHAR(50),
    Notes NVARCHAR(500),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT FK_InventoryMovements_FromLocation FOREIGN KEY (FromLocationId) REFERENCES StorageLocations(Id),
    CONSTRAINT FK_InventoryMovements_ToLocation FOREIGN KEY (ToLocationId) REFERENCES StorageLocations(Id),
    CONSTRAINT FK_InventoryMovements_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_InventoryMovements_Type CHECK (MovementType IN ('Inbound', 'Outbound', 'Transfer', 'Adjustment')),
    CONSTRAINT CK_InventoryMovements_ItemType CHECK (ItemType IN ('RawMaterial', 'FinishedProduct'))
);
```

### 7. Financial Management

#### Customers
```sql
CREATE TABLE Customers (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Code NVARCHAR(20) NOT NULL UNIQUE,
    Name NVARCHAR(200) NOT NULL,
    ContactPerson NVARCHAR(100),
    Email NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(500),
    City NVARCHAR(100),
    Country NVARCHAR(100),
    TaxNumber NVARCHAR(20),
    CreditLimit DECIMAL(18,2) DEFAULT 0,
    PaymentTerms INT DEFAULT 30, -- Days
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    CustomerType NVARCHAR(20) NOT NULL DEFAULT 'Retail', -- 'Retail', 'Wholesale', 'Distributor'
    IsActive BIT NOT NULL DEFAULT 1,
    Notes NVARCHAR(1000),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Customers_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Customers_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id),
    CONSTRAINT CK_Customers_Type CHECK (CustomerType IN ('Retail', 'Wholesale', 'Distributor'))
);
```

#### Suppliers
```sql
CREATE TABLE Suppliers (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Code NVARCHAR(20) NOT NULL UNIQUE,
    Name NVARCHAR(200) NOT NULL,
    ContactPerson NVARCHAR(100),
    Email NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    Address NVARCHAR(500),
    City NVARCHAR(100),
    Country NVARCHAR(100),
    TaxNumber NVARCHAR(20),
    PaymentTerms INT DEFAULT 30, -- Days
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    SupplierType NVARCHAR(20) NOT NULL DEFAULT 'Material', -- 'Material', 'Service', 'Equipment'
    IsActive BIT NOT NULL DEFAULT 1,
    Notes NVARCHAR(1000),
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Suppliers_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT FK_Suppliers_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id),
    CONSTRAINT CK_Suppliers_Type CHECK (SupplierType IN ('Material', 'Service', 'Equipment'))
);
```

#### Departments
```sql
CREATE TABLE Departments (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    Code NVARCHAR(20) NOT NULL UNIQUE,
    Name NVARCHAR(100) NOT NULL,
    Description NVARCHAR(255),
    ManagerId UNIQUEIDENTIFIER,
    BudgetLimit DECIMAL(18,2),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Departments_Manager FOREIGN KEY (ManagerId) REFERENCES Users(Id),
    CONSTRAINT FK_Departments_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);
```

#### Employees
```sql
CREATE TABLE Employees (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    EmployeeNumber NVARCHAR(20) NOT NULL UNIQUE,
    UserId UNIQUEIDENTIFIER,
    FirstName NVARCHAR(50) NOT NULL,
    LastName NVARCHAR(50) NOT NULL,
    Email NVARCHAR(100),
    PhoneNumber NVARCHAR(20),
    DepartmentId UNIQUEIDENTIFIER,
    Position NVARCHAR(100),
    HireDate DATE NOT NULL,
    Salary DECIMAL(18,2),
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_Employees_User FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT FK_Employees_Department FOREIGN KEY (DepartmentId) REFERENCES Departments(Id),
    CONSTRAINT FK_Employees_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id)
);
```

#### FinancialAccounts
```sql
CREATE TABLE FinancialAccounts (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    AccountCode NVARCHAR(20) NOT NULL UNIQUE,
    AccountName NVARCHAR(200) NOT NULL,
    AccountType NVARCHAR(50) NOT NULL, -- 'Asset', 'Liability', 'Equity', 'Revenue', 'Expense'
    ParentAccountId UNIQUEIDENTIFIER,
    CurrentBalance DECIMAL(18,2) DEFAULT 0,
    IsActive BIT NOT NULL DEFAULT 1,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_FinancialAccounts_Parent FOREIGN KEY (ParentAccountId) REFERENCES FinancialAccounts(Id),
    CONSTRAINT FK_FinancialAccounts_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_FinancialAccounts_Type CHECK (AccountType IN ('Asset', 'Liability', 'Equity', 'Revenue', 'Expense'))
);
```

#### FinancialTransactions
```sql
CREATE TABLE FinancialTransactions (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TransactionNumber NVARCHAR(50) NOT NULL UNIQUE,
    TransactionDate DATETIME2 NOT NULL,
    TransactionType NVARCHAR(50) NOT NULL, -- 'Sale', 'Purchase', 'Payment', 'Receipt', 'Expense', 'Revenue'
    EntityType NVARCHAR(20), -- 'Customer', 'Supplier', 'Partner', 'Employee', 'Department'
    EntityId UNIQUEIDENTIFIER,
    TotalAmount DECIMAL(18,2) NOT NULL,
    Description NVARCHAR(500),
    ReferenceNumber NVARCHAR(50),
    Status NVARCHAR(20) NOT NULL DEFAULT 'Pending', -- 'Pending', 'Approved', 'Cancelled'
    ApprovedBy UNIQUEIDENTIFIER,
    ApprovedDate DATETIME2,
    CreatedDate DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CreatedBy UNIQUEIDENTIFIER NOT NULL,
    CONSTRAINT FK_FinancialTransactions_ApprovedBy FOREIGN KEY (ApprovedBy) REFERENCES Users(Id),
    CONSTRAINT FK_FinancialTransactions_CreatedBy FOREIGN KEY (CreatedBy) REFERENCES Users(Id),
    CONSTRAINT CK_FinancialTransactions_Status CHECK (Status IN ('Pending', 'Approved', 'Cancelled'))
);
```

#### FinancialTransactionDetails
```sql
CREATE TABLE FinancialTransactionDetails (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TransactionId UNIQUEIDENTIFIER NOT NULL,
    AccountId UNIQUEIDENTIFIER NOT NULL,
    DebitAmount DECIMAL(18,2) DEFAULT 0,
    CreditAmount DECIMAL(18,2) DEFAULT 0,
    Description NVARCHAR(255),
    CONSTRAINT FK_FinancialTransactionDetails_Transaction FOREIGN KEY (TransactionId) REFERENCES FinancialTransactions(Id) ON DELETE CASCADE,
    CONSTRAINT FK_FinancialTransactionDetails_Account FOREIGN KEY (AccountId) REFERENCES FinancialAccounts(Id),
    CONSTRAINT CK_FinancialTransactionDetails_Amount CHECK ((DebitAmount > 0 AND CreditAmount = 0) OR (CreditAmount > 0 AND DebitAmount = 0))
);
```

### 8. System Configuration

#### SystemSettings
```sql
CREATE TABLE SystemSettings (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    SettingKey NVARCHAR(100) NOT NULL UNIQUE,
    SettingValue NVARCHAR(1000),
    SettingType NVARCHAR(20) NOT NULL, -- 'String', 'Number', 'Boolean', 'Date'
    Description NVARCHAR(255),
    IsEditable BIT NOT NULL DEFAULT 1,
    ModifiedDate DATETIME2,
    ModifiedBy UNIQUEIDENTIFIER,
    CONSTRAINT FK_SystemSettings_ModifiedBy FOREIGN KEY (ModifiedBy) REFERENCES Users(Id),
    CONSTRAINT CK_SystemSettings_Type CHECK (SettingType IN ('String', 'Number', 'Boolean', 'Date'))
);
```

#### AuditLogs
```sql
CREATE TABLE AuditLogs (
    Id UNIQUEIDENTIFIER PRIMARY KEY DEFAULT NEWID(),
    TableName NVARCHAR(100) NOT NULL,
    RecordId UNIQUEIDENTIFIER NOT NULL,
    Action NVARCHAR(20) NOT NULL, -- 'Insert', 'Update', 'Delete'
    OldValues NVARCHAR(MAX),
    NewValues NVARCHAR(MAX),
    ChangedFields NVARCHAR(500),
    UserId UNIQUEIDENTIFIER,
    UserName NVARCHAR(100),
    IpAddress NVARCHAR(45),
    UserAgent NVARCHAR(500),
    Timestamp DATETIME2 NOT NULL DEFAULT GETUTCDATE(),
    CONSTRAINT FK_AuditLogs_User FOREIGN KEY (UserId) REFERENCES Users(Id),
    CONSTRAINT CK_AuditLogs_Action CHECK (Action IN ('Insert', 'Update', 'Delete'))
);
```

## Additional Indexes for Performance

```sql
-- Inventory Movements
CREATE INDEX IX_InventoryMovements_Item ON InventoryMovements(ItemType, ItemId);
CREATE INDEX IX_InventoryMovements_Date ON InventoryMovements(MovementDate);
CREATE INDEX IX_InventoryMovements_Reference ON InventoryMovements(ReferenceType, ReferenceId);

-- Financial Transactions
CREATE INDEX IX_FinancialTransactions_Date ON FinancialTransactions(TransactionDate);
CREATE INDEX IX_FinancialTransactions_Entity ON FinancialTransactions(EntityType, EntityId);
CREATE INDEX IX_FinancialTransactions_Type ON FinancialTransactions(TransactionType);
CREATE INDEX IX_FinancialTransactions_Status ON FinancialTransactions(Status);

-- Customers and Suppliers
CREATE INDEX IX_Customers_Code ON Customers(Code);
CREATE INDEX IX_Customers_Name ON Customers(Name);
CREATE INDEX IX_Suppliers_Code ON Suppliers(Code);
CREATE INDEX IX_Suppliers_Name ON Suppliers(Name);

-- Audit Logs
CREATE INDEX IX_AuditLogs_Table_Record ON AuditLogs(TableName, RecordId);
CREATE INDEX IX_AuditLogs_User_Date ON AuditLogs(UserId, Timestamp);
```

## Next Steps
1. Create Entity Framework Core models
2. Implement database migrations
3. Add seed data for initial setup
4. Create stored procedures for complex calculations
5. Implement audit logging triggers
6. Add computed columns for real-time calculations
7. Create views for reporting purposes
