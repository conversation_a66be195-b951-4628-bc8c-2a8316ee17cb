<nav class="navbar" [attr.dir]="currentLanguage.direction">
  <div class="navbar-container">
    <!-- Left side: Menu toggle and breadcrumbs -->
    <div class="navbar-left">
      <!-- Menu toggle button -->
      <button class="menu-toggle-btn"
              (click)="onToggleSidebar()"
              [attr.aria-label]="isRTL ? 'تبديل القائمة' : 'Toggle menu'">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M4 6h16M4 12h16M4 18h16"></path>
        </svg>
      </button>

      <!-- Breadcrumbs placeholder -->
      <div class="breadcrumbs">
        <span class="text-sm text-gray-600">
          {{ isRTL ? 'لوحة التحكم' : 'Dashboard' }}
        </span>
      </div>
    </div>

    <!-- Right side: Theme toggle, language switcher, and user info -->
    <div class="navbar-right">
      <!-- Theme toggle -->
      <app-theme-toggle></app-theme-toggle>

      <!-- Language switcher -->
      <app-language-switcher></app-language-switcher>

      <!-- Notifications (placeholder) -->
      <button class="notification-btn"
              [attr.aria-label]="isRTL ? 'الإشعارات' : 'Notifications'">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9"></path>
        </svg>
        <span class="notification-badge">3</span>
      </button>

      <!-- User menu -->
      <div class="user-menu">
        <button class="user-menu-btn"
                [attr.aria-label]="isRTL ? 'قائمة المستخدم' : 'User menu'">
          <div class="user-avatar">
            <span class="avatar-text">
              {{ currentUser?.fullName?.charAt(0) || 'U' }}
            </span>
          </div>
          <div class="user-info" *ngIf="!isMobile">
            <span class="user-name">{{ currentUser?.fullName || 'User' }}</span>
            <span class="user-role">{{ isRTL ? 'مدير' : 'Admin' }}</span>
          </div>
          <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
          </svg>
        </button>

        <!-- User dropdown menu (placeholder) -->
        <div class="user-dropdown hidden">
          <a href="#" class="dropdown-item">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"></path>
            </svg>
            {{ isRTL ? 'الملف الشخصي' : 'Profile' }}
          </a>
          <a href="#" class="dropdown-item">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
            </svg>
            {{ isRTL ? 'الإعدادات' : 'Settings' }}
          </a>
          <hr class="dropdown-divider">
          <button (click)="logout()" class="dropdown-item text-red-600">
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
            </svg>
            {{ isRTL ? 'تسجيل الخروج' : 'Logout' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</nav>
