import { Injectable } from '@angular/core';
import { ToastrService, IndividualConfig } from 'ngx-toastr';
import { LanguageService } from './language';

export interface NotificationConfig extends Partial<IndividualConfig> {
  title?: string;
  message: string;
  duration?: number;
}

@Injectable({
  providedIn: 'root'
})
export class NotificationService {
  private readonly defaultConfig: Partial<IndividualConfig> = {
    timeOut: 5000,
    closeButton: true,
    progressBar: true,
    enableHtml: false,
    positionClass: 'toast-top-right'
  };

  constructor(
    private toastr: ToastrService,
    private languageService: LanguageService
  ) {
    // Update toast position based on language direction
    this.languageService.currentLanguage$.subscribe(language => {
      this.updateToastPosition(language.direction);
    });
  }

  private updateToastPosition(direction: 'ltr' | 'rtl'): void {
    // Update default position based on text direction
    this.defaultConfig.positionClass = direction === 'rtl'
      ? 'toast-top-left'
      : 'toast-top-right';
  }

  private getConfig(config?: Partial<IndividualConfig>): Partial<IndividualConfig> {
    return {
      ...this.defaultConfig,
      ...config
    };
  }

  success(config: NotificationConfig): void {
    const toastConfig = this.getConfig(config);
    this.toastr.success(config.message, config.title, toastConfig);
  }

  error(config: NotificationConfig): void {
    const toastConfig = this.getConfig({
      ...config,
      timeOut: config.duration || 8000 // Longer duration for errors
    });
    this.toastr.error(config.message, config.title, toastConfig);
  }

  warning(config: NotificationConfig): void {
    const toastConfig = this.getConfig(config);
    this.toastr.warning(config.message, config.title, toastConfig);
  }

  info(config: NotificationConfig): void {
    const toastConfig = this.getConfig(config);
    this.toastr.info(config.message, config.title, toastConfig);
  }

  // Convenience methods with predefined messages
  showSuccess(message: string, title?: string): void {
    this.success({ message, title });
  }

  showError(message: string, title?: string): void {
    this.error({ message, title });
  }

  showWarning(message: string, title?: string): void {
    this.warning({ message, title });
  }

  showInfo(message: string, title?: string): void {
    this.info({ message, title });
  }

  // API response handlers
  handleApiSuccess(message?: string): void {
    this.showSuccess(
      message || 'Operation completed successfully',
      'Success'
    );
  }

  handleApiError(error: any): void {
    let message = 'An error occurred';

    if (error?.error?.message) {
      message = error.error.message;
    } else if (error?.message) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    }

    this.showError(message, 'Error');
  }

  handleValidationErrors(errors: { [key: string]: string[] }): void {
    Object.keys(errors).forEach(field => {
      const fieldErrors = errors[field];
      if (fieldErrors && fieldErrors.length > 0) {
        fieldErrors.forEach(errorMessage => {
          this.showError(errorMessage, `${field} Error`);
        });
      }
    });
  }

  // Clear all toasts
  clear(): void {
    this.toastr.clear();
  }

  // Network status notifications
  showNetworkError(): void {
    this.showError(
      'Network connection error. Please check your internet connection.',
      'Connection Error'
    );
  }

  showServerError(): void {
    this.showError(
      'Server error occurred. Please try again later.',
      'Server Error'
    );
  }

  showUnauthorizedError(): void {
    this.showError(
      'You are not authorized to perform this action.',
      'Unauthorized'
    );
  }

  showSessionExpired(): void {
    this.showWarning(
      'Your session has expired. Please log in again.',
      'Session Expired'
    );
  }
}
