<div class="dashboard" [attr.dir]="currentLanguage.direction">
  <!-- Dashboard Header -->
  <div class="dashboard-header">
    <h1 class="dashboard-title">
      {{ isRTL ? 'لوحة التحكم' : 'Dashboard' }}
    </h1>
    <p class="dashboard-subtitle">
      {{ isRTL ? 'مرحباً بك في نظام إدارة الأبواب المدرعة' : 'Welcome to Armored Doors Management System' }}
    </p>
  </div>

  <!-- Dashboard Stats -->
  <div class="stats-grid">
    <div class="stat-card">
      <div class="stat-icon bg-blue-100 text-blue-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10"></path>
        </svg>
      </div>
      <div class="stat-content">
        <h3 class="stat-value">1,234</h3>
        <p class="stat-label">{{ isRTL ? 'المواد الخام' : 'Raw Materials' }}</p>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon bg-green-100 text-green-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8"></path>
        </svg>
      </div>
      <div class="stat-content">
        <h3 class="stat-value">567</h3>
        <p class="stat-label">{{ isRTL ? 'المنتجات النهائية' : 'Finished Products' }}</p>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon bg-yellow-100 text-yellow-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
        </svg>
      </div>
      <div class="stat-content">
        <h3 class="stat-value">89</h3>
        <p class="stat-label">{{ isRTL ? 'أوامر العمل' : 'Work Orders' }}</p>
      </div>
    </div>

    <div class="stat-card">
      <div class="stat-icon bg-purple-100 text-purple-600">
        <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857"></path>
        </svg>
      </div>
      <div class="stat-content">
        <h3 class="stat-value">156</h3>
        <p class="stat-label">{{ isRTL ? 'العملاء' : 'Customers' }}</p>
      </div>
    </div>
  </div>

  <!-- Dashboard Content -->
  <div class="dashboard-content">
    <div class="content-grid">
      <!-- Recent Orders -->
      <div class="content-card">
        <div class="card-header">
          <h2 class="card-title">{{ isRTL ? 'الطلبات الحديثة' : 'Recent Orders' }}</h2>
          <button class="btn-secondary">{{ isRTL ? 'عرض الكل' : 'View All' }}</button>
        </div>
        <div class="card-content">
          <div class="order-item">
            <div class="order-info">
              <h4 class="order-title">{{ isRTL ? 'باب مدرع سكني' : 'Residential Armored Door' }}</h4>
              <p class="order-customer">{{ isRTL ? 'شركة الأهرام للمقاولات' : 'Ahram Contracting Company' }}</p>
            </div>
            <div class="order-status">
              <span class="status-badge status-pending">{{ isRTL ? 'قيد التنفيذ' : 'In Progress' }}</span>
            </div>
          </div>
          <div class="order-item">
            <div class="order-info">
              <h4 class="order-title">{{ isRTL ? 'باب أمني تجاري' : 'Commercial Security Door' }}</h4>
              <p class="order-customer">{{ isRTL ? 'مجموعة النصر للتطوير' : 'Nasr Development Group' }}</p>
            </div>
            <div class="order-status">
              <span class="status-badge status-completed">{{ isRTL ? 'مكتمل' : 'Completed' }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Inventory Alerts -->
      <div class="content-card">
        <div class="card-header">
          <h2 class="card-title">{{ isRTL ? 'تنبيهات المخزون' : 'Inventory Alerts' }}</h2>
          <button class="btn-secondary">{{ isRTL ? 'إدارة المخزون' : 'Manage Inventory' }}</button>
        </div>
        <div class="card-content">
          <div class="alert-item">
            <div class="alert-icon text-red-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 15.5c-.77.833.192 2.5 1.732 2.5z"></path>
              </svg>
            </div>
            <div class="alert-content">
              <h4 class="alert-title">{{ isRTL ? 'مخزون منخفض' : 'Low Stock' }}</h4>
              <p class="alert-description">{{ isRTL ? 'صلب مقاوم للصدأ - 5 وحدات متبقية' : 'Stainless Steel - 5 units remaining' }}</p>
            </div>
          </div>
          <div class="alert-item">
            <div class="alert-icon text-yellow-600">
              <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>
            </div>
            <div class="alert-content">
              <h4 class="alert-title">{{ isRTL ? 'إعادة الطلب' : 'Reorder Required' }}</h4>
              <p class="alert-description">{{ isRTL ? 'أقفال أمنية - 12 وحدة متبقية' : 'Security Locks - 12 units remaining' }}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
