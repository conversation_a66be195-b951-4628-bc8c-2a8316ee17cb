using System.ComponentModel.DataAnnotations;

namespace ArmoredDoors.Core.Entities;

public class SystemSetting : BaseEntity
{
    [Required]
    [StringLength(100)]
    public string SettingKey { get; set; } = string.Empty;

    [StringLength(1000)]
    public string? SettingValue { get; set; }

    [Required]
    [StringLength(20)]
    public string SettingType { get; set; } = string.Empty; // 'String', 'Number', 'Boolean', 'Date'

    [StringLength(255)]
    public string? Description { get; set; }

    public bool IsEditable { get; set; } = true;

    // Navigation properties
    public virtual User? ModifiedByUser { get; set; }
}

public class AuditLog : BaseEntity
{
    [Required]
    [StringLength(100)]
    public string TableName { get; set; } = string.Empty;

    public Guid RecordId { get; set; }

    [Required]
    [StringLength(20)]
    public string Action { get; set; } = string.Empty; // 'Insert', 'Update', 'Delete'

    public string? OldValues { get; set; }

    public string? NewValues { get; set; }

    [StringLength(500)]
    public string? ChangedFields { get; set; }

    public Guid? UserId { get; set; }

    [StringLength(100)]
    public string? UserName { get; set; }

    [StringLength(45)]
    public string? IpAddress { get; set; }

    [StringLength(500)]
    public string? UserAgent { get; set; }

    public DateTime Timestamp { get; set; } = DateTime.UtcNow;

    // Navigation properties
    public virtual User? User { get; set; }
}
