using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArmoredDoors.Core.Entities;

public class Category : BaseActiveEntity
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Description { get; set; }

    [Required]
    [StringLength(20)]
    public string CategoryType { get; set; } = string.Empty; // 'RawMaterial', 'FinishedProduct'

    public Guid? ParentCategoryId { get; set; }

    // Navigation properties
    public virtual Category? ParentCategory { get; set; }
    public virtual ICollection<Category> SubCategories { get; set; } = new List<Category>();
    public virtual ICollection<RawMaterial> RawMaterials { get; set; } = new List<RawMaterial>();
    public virtual ICollection<FinishedProduct> FinishedProducts { get; set; } = new List<FinishedProduct>();
}

public class MeasurementUnit : BaseActiveEntity
{
    [Required]
    [StringLength(50)]
    public string Name { get; set; } = string.Empty;

    [Required]
    [StringLength(10)]
    public string Symbol { get; set; } = string.Empty;

    [Required]
    [StringLength(20)]
    public string UnitType { get; set; } = string.Empty; // 'Weight', 'Count', 'Length', 'Volume', 'Area'

    [StringLength(50)]
    public string? BaseUnit { get; set; }

    [Column(TypeName = "decimal(18,6)")]
    public decimal? ConversionFactor { get; set; }

    // Navigation properties
    public virtual ICollection<RawMaterial> RawMaterials { get; set; } = new List<RawMaterial>();
    public virtual ICollection<FinishedProduct> FinishedProducts { get; set; } = new List<FinishedProduct>();
}

public class StorageLocation : BaseActiveEntity
{
    [Required]
    [StringLength(20)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Description { get; set; }

    [Required]
    [StringLength(20)]
    public string LocationType { get; set; } = string.Empty; // 'Warehouse', 'Section', 'Shelf', 'Bin'

    public Guid? ParentLocationId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? Capacity { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentOccupancy { get; set; } = 0;

    // Navigation properties
    public virtual StorageLocation? ParentLocation { get; set; }
    public virtual ICollection<StorageLocation> SubLocations { get; set; } = new List<StorageLocation>();
    public virtual ICollection<RawMaterial> RawMaterials { get; set; } = new List<RawMaterial>();
    public virtual ICollection<FinishedProduct> FinishedProducts { get; set; } = new List<FinishedProduct>();
    public virtual ICollection<InventoryMovement> FromMovements { get; set; } = new List<InventoryMovement>();
    public virtual ICollection<InventoryMovement> ToMovements { get; set; } = new List<InventoryMovement>();
}

public class RawMaterial : BaseActiveEntity
{
    [Required]
    [StringLength(50)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    public Guid CategoryId { get; set; }
    public Guid MeasurementUnitId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal MinimumStockLevel { get; set; } = 0;

    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaximumStockLevel { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? ReorderPoint { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? StandardCost { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentStockQuantity { get; set; } = 0;

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentStockValue { get; set; } = 0;

    public Guid? DefaultStorageLocationId { get; set; }

    // Navigation properties
    public virtual Category Category { get; set; } = null!;
    public virtual MeasurementUnit MeasurementUnit { get; set; } = null!;
    public virtual StorageLocation? DefaultStorageLocation { get; set; }
    public virtual ICollection<RawMaterialImage> Images { get; set; } = new List<RawMaterialImage>();
    public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
}

public class RawMaterialImage : BaseEntity
{
    public Guid RawMaterialId { get; set; }

    [Required]
    [StringLength(500)]
    public string ImagePath { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string ImageName { get; set; } = string.Empty;

    public long? ImageSize { get; set; }

    public bool IsPrimary { get; set; } = false;

    public int DisplayOrder { get; set; } = 0;

    public DateTime UploadedDate { get; set; } = DateTime.UtcNow;
    public Guid? UploadedBy { get; set; }

    // Navigation properties
    public virtual RawMaterial RawMaterial { get; set; } = null!;
    public virtual User? UploadedByUser { get; set; }
}

public class FinishedProduct : BaseActiveEntity
{
    [Required]
    [StringLength(50)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(500)]
    public string? Description { get; set; }

    public Guid CategoryId { get; set; }
    public Guid MeasurementUnitId { get; set; }

    [StringLength(50)]
    public string? Barcode { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal MinimumStockLevel { get; set; } = 0;

    [Column(TypeName = "decimal(18,2)")]
    public decimal? MaximumStockLevel { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? ReorderPoint { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? StandardCost { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? SellingPrice { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? WholesalePrice { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentStockQuantity { get; set; } = 0;

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentStockValue { get; set; } = 0;

    public Guid? DefaultStorageLocationId { get; set; }

    // Navigation properties
    public virtual Category Category { get; set; } = null!;
    public virtual MeasurementUnit MeasurementUnit { get; set; } = null!;
    public virtual StorageLocation? DefaultStorageLocation { get; set; }
    public virtual ICollection<FinishedProductImage> Images { get; set; } = new List<FinishedProductImage>();
    public virtual ICollection<InventoryMovement> InventoryMovements { get; set; } = new List<InventoryMovement>();
}

public class FinishedProductImage : BaseEntity
{
    public Guid FinishedProductId { get; set; }

    [Required]
    [StringLength(500)]
    public string ImagePath { get; set; } = string.Empty;

    [Required]
    [StringLength(255)]
    public string ImageName { get; set; } = string.Empty;

    public long? ImageSize { get; set; }

    public bool IsPrimary { get; set; } = false;

    public int DisplayOrder { get; set; } = 0;

    public DateTime UploadedDate { get; set; } = DateTime.UtcNow;
    public Guid? UploadedBy { get; set; }

    // Navigation properties
    public virtual FinishedProduct FinishedProduct { get; set; } = null!;
    public virtual User? UploadedByUser { get; set; }
}

public class InventoryMovement : BaseEntity
{
    [Required]
    [StringLength(20)]
    public string MovementType { get; set; } = string.Empty; // 'Inbound', 'Outbound', 'Transfer', 'Adjustment'

    [Required]
    [StringLength(20)]
    public string ItemType { get; set; } = string.Empty; // 'RawMaterial', 'FinishedProduct'

    public Guid ItemId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal Quantity { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? UnitCost { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? TotalCost { get; set; }

    public Guid? FromLocationId { get; set; }
    public Guid? ToLocationId { get; set; }

    public DateTime MovementDate { get; set; } = DateTime.UtcNow;

    [StringLength(50)]
    public string? ReferenceType { get; set; } // 'Purchase', 'Sale', 'Production', 'Transfer', 'Adjustment'

    public Guid? ReferenceId { get; set; }

    [StringLength(50)]
    public string? ReferenceNumber { get; set; }

    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation properties
    public virtual StorageLocation? FromLocation { get; set; }
    public virtual StorageLocation? ToLocation { get; set; }
    public virtual User CreatedByUser { get; set; } = null!;

    // Helper properties for polymorphic relationships
    public virtual RawMaterial? RawMaterial { get; set; }
    public virtual FinishedProduct? FinishedProduct { get; set; }
}
