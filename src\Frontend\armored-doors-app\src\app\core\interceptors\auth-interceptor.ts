import { Injectable } from '@angular/core';
import { HttpInterceptor, HttpRequest, HttpHandler, HttpEvent, HttpErrorResponse } from '@angular/common/http';
import { Observable, throwError, BehaviorSubject } from 'rxjs';
import { catchError, switchMap, filter, take } from 'rxjs/operators';
import { AuthService } from '../services/auth';
import { NotificationService } from '../services/notification';

@Injectable()
export class AuthInterceptor implements HttpInterceptor {
  private isRefreshing = false;
  private refreshTokenSubject: BehaviorSubject<any> = new BehaviorSubject<any>(null);

  constructor(
    private authService: AuthService,
    private notificationService: NotificationService
  ) {}

  intercept(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    // Add auth header if token exists and request is not for auth endpoints
    if (this.shouldAddAuthHeader(request)) {
      request = this.addAuthHeader(request);
    }

    return next.handle(request).pipe(
      catchError((error: HttpErrorResponse) => {
        if (error.status === 401 && this.shouldHandleUnauthorized(request)) {
          return this.handle401Error(request, next);
        }

        // Handle other HTTP errors
        this.handleHttpError(error);
        return throwError(() => error);
      })
    );
  }

  private shouldAddAuthHeader(request: HttpRequest<any>): boolean {
    const token = this.authService.getToken();
    const isAuthEndpoint = this.isAuthEndpoint(request.url);

    return !!token && !isAuthEndpoint;
  }

  private isAuthEndpoint(url: string): boolean {
    const authEndpoints = ['/auth/login', '/auth/refresh', '/auth/register'];
    return authEndpoints.some(endpoint => url.includes(endpoint));
  }

  private addAuthHeader(request: HttpRequest<any>): HttpRequest<any> {
    const token = this.authService.getToken();
    return request.clone({
      setHeaders: {
        Authorization: `Bearer ${token}`
      }
    });
  }

  private shouldHandleUnauthorized(request: HttpRequest<any>): boolean {
    // Don't handle 401 for refresh token requests to avoid infinite loops
    return !request.url.includes('/auth/refresh') && !request.url.includes('/auth/login');
  }

  private handle401Error(request: HttpRequest<any>, next: HttpHandler): Observable<HttpEvent<any>> {
    if (!this.isRefreshing) {
      this.isRefreshing = true;
      this.refreshTokenSubject.next(null);

      return this.authService.refreshToken().pipe(
        switchMap((tokenResponse: any) => {
          this.isRefreshing = false;
          this.refreshTokenSubject.next(tokenResponse.token);

          // Retry the original request with new token
          const newRequest = this.addAuthHeader(request);
          return next.handle(newRequest);
        }),
        catchError((error) => {
          this.isRefreshing = false;
          this.authService.logout();
          this.notificationService.showSessionExpired();
          return throwError(() => error);
        })
      );
    } else {
      // Wait for refresh to complete
      return this.refreshTokenSubject.pipe(
        filter(token => token != null),
        take(1),
        switchMap(() => {
          const newRequest = this.addAuthHeader(request);
          return next.handle(newRequest);
        })
      );
    }
  }

  private handleHttpError(error: HttpErrorResponse): void {
    switch (error.status) {
      case 0:
        // Network error
        this.notificationService.showNetworkError();
        break;
      case 403:
        this.notificationService.showUnauthorizedError();
        break;
      case 500:
      case 502:
      case 503:
      case 504:
        this.notificationService.showServerError();
        break;
      default:
        // Let the component handle specific errors
        break;
    }
  }
}
