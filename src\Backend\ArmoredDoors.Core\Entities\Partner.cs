using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArmoredDoors.Core.Entities;

public class Partner : BaseActiveEntity
{
    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [StringLength(20)]
    public string? NationalId { get; set; }

    [StringLength(20)]
    public string? TaxNumber { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal InitialCapitalAmount { get; set; } = 0;

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentCapitalAmount { get; set; } = 0;

    [Column(TypeName = "decimal(5,4)")]
    public decimal CurrentOwnershipPercentage { get; set; } = 0;

    public DateTime JoinDate { get; set; }

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation properties
    public virtual ICollection<CapitalTransaction> CapitalTransactions { get; set; } = new List<CapitalTransaction>();
    public virtual ICollection<ProfitDistribution> ProfitDistributions { get; set; } = new List<ProfitDistribution>();
}

public class CapitalTransaction : BaseEntity
{
    public Guid PartnerId { get; set; }

    [Required]
    [StringLength(20)]
    public string TransactionType { get; set; } = string.Empty; // 'Injection', 'Withdrawal'

    [Column(TypeName = "decimal(18,2)")]
    public decimal Amount { get; set; }

    public DateTime TransactionDate { get; set; } = DateTime.UtcNow;

    [StringLength(500)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? ReferenceNumber { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal PreviousCapitalAmount { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal NewCapitalAmount { get; set; }

    [Column(TypeName = "decimal(5,4)")]
    public decimal PreviousOwnershipPercentage { get; set; }

    [Column(TypeName = "decimal(5,4)")]
    public decimal NewOwnershipPercentage { get; set; }

    public Guid? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }

    // Navigation properties
    public virtual Partner Partner { get; set; } = null!;
    public virtual User? ApprovedByUser { get; set; }
    public virtual User CreatedByUser { get; set; } = null!;
}

public class ProfitDistribution : BaseEntity
{
    public Guid PartnerId { get; set; }

    public DateTime PeriodStartDate { get; set; }
    public DateTime PeriodEndDate { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalCompanyProfit { get; set; }

    [Column(TypeName = "decimal(5,4)")]
    public decimal PartnerOwnershipPercentage { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal PartnerProfitShare { get; set; }

    public DateTime? DistributionDate { get; set; }

    [Required]
    [StringLength(20)]
    public string Status { get; set; } = "Calculated"; // 'Calculated', 'Distributed', 'Cancelled'

    [StringLength(500)]
    public string? Notes { get; set; }

    // Navigation properties
    public virtual Partner Partner { get; set; } = null!;
    public virtual User CreatedByUser { get; set; } = null!;
}
