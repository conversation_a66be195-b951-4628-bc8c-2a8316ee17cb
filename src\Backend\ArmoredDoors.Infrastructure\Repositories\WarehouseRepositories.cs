using Microsoft.EntityFrameworkCore;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Interfaces;
using ArmoredDoors.Infrastructure.Data;

namespace ArmoredDoors.Infrastructure.Repositories;

public class FinishedProductRepository : Repository<FinishedProduct>, IFinishedProductRepository
{
    public FinishedProductRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<FinishedProduct?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(fp => fp.Category)
            .Include(fp => fp.MeasurementUnit)
            .Include(fp => fp.DefaultStorageLocation)
            .Include(fp => fp.Images)
            .FirstOrDefaultAsync(fp => fp.Code == code, cancellationToken);
    }

    public async Task<FinishedProduct?> GetByBarcodeAsync(string barcode, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(fp => fp.Category)
            .Include(fp => fp.MeasurementUnit)
            .Include(fp => fp.DefaultStorageLocation)
            .Include(fp => fp.Images)
            .FirstOrDefaultAsync(fp => fp.Barcode == barcode, cancellationToken);
    }

    public async Task<IEnumerable<FinishedProduct>> GetByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(fp => fp.Category)
            .Include(fp => fp.MeasurementUnit)
            .Where(fp => fp.CategoryId == categoryId && fp.IsActive)
            .OrderBy(fp => fp.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<FinishedProduct>> GetLowStockItemsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(fp => fp.Category)
            .Include(fp => fp.MeasurementUnit)
            .Where(fp => fp.IsActive && fp.CurrentStockQuantity <= fp.MinimumStockLevel)
            .OrderBy(fp => fp.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(fp => fp.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(fp => fp.Id != excludeId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task<bool> IsBarcodeUniqueAsync(string barcode, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(fp => fp.Barcode == barcode);
        
        if (excludeId.HasValue)
        {
            query = query.Where(fp => fp.Id != excludeId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task UpdateStockQuantityAsync(Guid id, decimal quantity, CancellationToken cancellationToken = default)
    {
        var finishedProduct = await GetByIdAsync(id, cancellationToken);
        if (finishedProduct != null)
        {
            finishedProduct.CurrentStockQuantity = quantity;
            finishedProduct.ModifiedDate = DateTime.UtcNow;
            _dbSet.Update(finishedProduct);
        }
    }
}

public class InventoryMovementRepository : Repository<InventoryMovement>, IInventoryMovementRepository
{
    public InventoryMovementRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<InventoryMovement>> GetByItemAsync(string itemType, Guid itemId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(im => im.FromLocation)
            .Include(im => im.ToLocation)
            .Include(im => im.CreatedByUser)
            .Where(im => im.ItemType == itemType && im.ItemId == itemId)
            .OrderByDescending(im => im.MovementDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<InventoryMovement>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(im => im.FromLocation)
            .Include(im => im.ToLocation)
            .Include(im => im.CreatedByUser)
            .Where(im => im.MovementDate >= fromDate && im.MovementDate <= toDate)
            .OrderByDescending(im => im.MovementDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<InventoryMovement>> GetByLocationAsync(Guid locationId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(im => im.FromLocation)
            .Include(im => im.ToLocation)
            .Include(im => im.CreatedByUser)
            .Where(im => im.FromLocationId == locationId || im.ToLocationId == locationId)
            .OrderByDescending(im => im.MovementDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<InventoryMovement>> GetByReferenceAsync(string referenceType, Guid referenceId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(im => im.FromLocation)
            .Include(im => im.ToLocation)
            .Include(im => im.CreatedByUser)
            .Where(im => im.ReferenceType == referenceType && im.ReferenceId == referenceId)
            .OrderByDescending(im => im.MovementDate)
            .ToListAsync(cancellationToken);
    }
}
