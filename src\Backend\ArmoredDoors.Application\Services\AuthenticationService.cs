using System.IdentityModel.Tokens.Jwt;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.IdentityModel.Tokens;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Interfaces;

namespace ArmoredDoors.Application.Services;

public interface IAuthenticationService
{
    Task<AuthenticationResult> AuthenticateAsync(string username, string password);
    Task<AuthenticationResult> RefreshTokenAsync(string refreshToken);
    Task<bool> RevokeTokenAsync(string refreshToken);
    string GenerateJwtToken(User user, IEnumerable<string> permissions);
    string GenerateRefreshToken();
}

public class AuthenticationService : IAuthenticationService
{
    private readonly IUnitOfWork _unitOfWork;
    private readonly IConfiguration _configuration;

    public AuthenticationService(IUnitOfWork unitOfWork, IConfiguration configuration)
    {
        _unitOfWork = unitOfWork;
        _configuration = configuration;
    }

    public async Task<AuthenticationResult> AuthenticateAsync(string username, string password)
    {
        var user = await _unitOfWork.Users.GetByUsernameAsync(username);
        
        if (user == null || !user.IsActive || !VerifyPassword(password, user.PasswordHash))
        {
            return new AuthenticationResult { Success = false, Message = "Invalid username or password" };
        }

        // Get user permissions
        var permissions = GetUserPermissions(user);

        // Generate tokens
        var jwtToken = GenerateJwtToken(user, permissions);
        var refreshToken = GenerateRefreshToken();

        // Update last login
        user.LastLoginDate = DateTime.UtcNow;
        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return new AuthenticationResult
        {
            Success = true,
            Token = jwtToken,
            RefreshToken = refreshToken,
            User = new UserInfo
            {
                Id = user.Id,
                Username = user.Username,
                Email = user.Email,
                FullName = user.FullName,
                Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList(),
                Permissions = permissions.ToList()
            }
        };
    }

    public async Task<AuthenticationResult> RefreshTokenAsync(string refreshToken)
    {
        // In a real implementation, you would store refresh tokens in the database
        // and validate them here. For now, we'll implement a basic version.
        
        // This is a simplified implementation - in production, you should:
        // 1. Store refresh tokens in database with expiration
        // 2. Validate the refresh token
        // 3. Check if it's not revoked
        // 4. Generate new tokens
        
        return new AuthenticationResult { Success = false, Message = "Refresh token functionality not fully implemented" };
    }

    public async Task<bool> RevokeTokenAsync(string refreshToken)
    {
        // In a real implementation, you would mark the refresh token as revoked in the database
        return await Task.FromResult(true);
    }

    public string GenerateJwtToken(User user, IEnumerable<string> permissions)
    {
        var jwtSettings = _configuration.GetSection("JWT");
        var secretKey = jwtSettings["SecretKey"];
        var issuer = jwtSettings["Issuer"];
        var audience = jwtSettings["Audience"];
        var expiryMinutes = int.Parse(jwtSettings["ExpiryInMinutes"] ?? "60");

        var key = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(secretKey!));
        var credentials = new SigningCredentials(key, SecurityAlgorithms.HmacSha256);

        var claims = new List<Claim>
        {
            new Claim(ClaimTypes.NameIdentifier, user.Id.ToString()),
            new Claim(ClaimTypes.Name, user.Username),
            new Claim(ClaimTypes.Email, user.Email),
            new Claim("FullName", user.FullName),
            new Claim(JwtRegisteredClaimNames.Jti, Guid.NewGuid().ToString()),
            new Claim(JwtRegisteredClaimNames.Iat, DateTimeOffset.UtcNow.ToUnixTimeSeconds().ToString(), ClaimValueTypes.Integer64)
        };

        // Add role claims
        foreach (var userRole in user.UserRoles)
        {
            claims.Add(new Claim(ClaimTypes.Role, userRole.Role.Name));
        }

        // Add permission claims
        foreach (var permission in permissions)
        {
            claims.Add(new Claim("permission", permission));
        }

        var token = new JwtSecurityToken(
            issuer: issuer,
            audience: audience,
            claims: claims,
            expires: DateTime.UtcNow.AddMinutes(expiryMinutes),
            signingCredentials: credentials
        );

        return new JwtSecurityTokenHandler().WriteToken(token);
    }

    public string GenerateRefreshToken()
    {
        var randomBytes = new byte[64];
        using var rng = RandomNumberGenerator.Create();
        rng.GetBytes(randomBytes);
        return Convert.ToBase64String(randomBytes);
    }

    private static bool VerifyPassword(string password, string hash)
    {
        // This is a simplified password verification
        // In production, use proper password hashing like BCrypt or Argon2
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ArmoredDoorsSalt"));
        var hashedPassword = Convert.ToBase64String(hashedBytes);
        return hashedPassword == hash;
    }

    private static IEnumerable<string> GetUserPermissions(User user)
    {
        var permissions = new HashSet<string>();

        foreach (var userRole in user.UserRoles)
        {
            foreach (var rolePermission in userRole.Role.RolePermissions)
            {
                permissions.Add(rolePermission.Permission.Name);
            }
        }

        return permissions;
    }
}

public class AuthenticationResult
{
    public bool Success { get; set; }
    public string? Message { get; set; }
    public string? Token { get; set; }
    public string? RefreshToken { get; set; }
    public UserInfo? User { get; set; }
}

public class UserInfo
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public List<string> Roles { get; set; } = new();
    public List<string> Permissions { get; set; } = new();
}
