{"version": 3, "file": "gsw-FR.js", "sourceRoot": "", "sources": ["gsw-FR.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,aAAa,EAAC,UAAU,EAAC,SAAS,EAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,CAAC,aAAa,EAAC,UAAU,EAAC,SAAS,EAAC,aAAa,EAAC,QAAQ,EAAC,QAAQ,CAAC,EAAC,CAAC,EAAC,CAAC,aAAa,EAAC,OAAO,EAAC,QAAQ,EAAC,UAAU,EAAC,OAAO,EAAC,OAAO,CAAC,CAAC,EAAC,CAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"Mitternacht\",\"am Morge\",\"zmittag\",\"am Namittag\",\"zaabig\",\"znacht\"],u,u],[[\"Mitternacht\",\"am Morge\",\"zmittag\",\"am Namittag\",\"zaabig\",\"znacht\"],u,[\"Mitternacht\",\"Morge\",\"Mittag\",\"Namittag\",\"Aabig\",\"Nacht\"]],[\"00:00\",[\"05:00\",\"12:00\"],[\"12:00\",\"14:00\"],[\"14:00\",\"18:00\"],[\"18:00\",\"24:00\"],[\"00:00\",\"05:00\"]]];\n"]}