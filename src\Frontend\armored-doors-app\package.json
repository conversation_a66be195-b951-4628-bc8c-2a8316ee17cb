{"name": "armored-doors-app", "version": "0.0.0", "scripts": {"ng": "ng", "start": "ng serve", "build": "ng build", "watch": "ng build --watch --configuration development", "test": "ng test", "serve:ssr:armored-doors-app": "node dist/armored-doors-app/server/server.mjs"}, "prettier": {"overrides": [{"files": "*.html", "options": {"parser": "angular"}}]}, "private": true, "dependencies": {"@angular/animations": "^20.0.5", "@angular/cdk": "^20.0.4", "@angular/common": "^20.0.0", "@angular/compiler": "^20.0.0", "@angular/core": "^20.0.0", "@angular/forms": "^20.0.0", "@angular/material": "^20.0.4", "@angular/platform-browser": "^20.0.0", "@angular/platform-server": "^20.0.0", "@angular/router": "^20.0.0", "@angular/ssr": "^20.0.4", "@microsoft/signalr": "^8.0.7", "express": "^5.1.0", "lucide-angular": "^0.523.0", "ngx-toastr": "^19.0.0", "rxjs": "~7.8.0", "tslib": "^2.3.0"}, "devDependencies": {"@angular/build": "^20.0.4", "@angular/cli": "^20.0.4", "@angular/compiler-cli": "^20.0.0", "@angular/localize": "^20.0.5", "@tailwindcss/forms": "^0.5.10", "@tailwindcss/typography": "^0.5.16", "@types/express": "^5.0.1", "@types/jasmine": "~5.1.0", "@types/node": "^20.17.19", "autoprefixer": "^10.4.21", "jasmine-core": "~5.7.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "~5.8.2"}}