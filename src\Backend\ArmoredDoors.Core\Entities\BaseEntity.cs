using System.ComponentModel.DataAnnotations;

namespace ArmoredDoors.Core.Entities;

public abstract class BaseEntity
{
    [Key]
    public Guid Id { get; set; } = Guid.NewGuid();
    
    public DateTime CreatedDate { get; set; } = DateTime.UtcNow;
    
    public Guid? CreatedBy { get; set; }
    
    public DateTime? ModifiedDate { get; set; }
    
    public Guid? ModifiedBy { get; set; }
}

public abstract class BaseActiveEntity : BaseEntity
{
    public bool IsActive { get; set; } = true;
}
