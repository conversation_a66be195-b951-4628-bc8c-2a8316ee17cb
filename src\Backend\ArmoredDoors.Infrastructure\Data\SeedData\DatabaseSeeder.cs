using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Constants;
using System.Security.Cryptography;
using System.Text;

namespace ArmoredDoors.Infrastructure.Data.SeedData;

public class DatabaseSeeder
{
    private readonly ArmoredDoorsDbContext _context;
    private readonly ILogger<DatabaseSeeder> _logger;

    public DatabaseSeeder(ArmoredDoorsDbContext context, ILogger<DatabaseSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting database seeding...");

            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();

            // Seed in order of dependencies
            await SeedPermissionsAsync();
            await SeedRolesAsync();
            await SeedRolePermissionsAsync();
            await SeedUsersAsync();
            await SeedSystemSettingsAsync();
            await SeedMeasurementUnitsAsync();
            await SeedCategoriesAsync();
            await SeedStorageLocationsAsync();
            await SeedFinancialAccountsAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("Database seeding completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while seeding the database.");
            throw;
        }
    }

    private async Task SeedPermissionsAsync()
    {
        if (await _context.Permissions.AnyAsync())
        {
            _logger.LogInformation("Permissions already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding permissions...");

        var permissions = Permissions.GetAllPermissions();
        var permissionEntities = permissions.Select(p => new Permission
        {
            Id = Guid.NewGuid(),
            Name = p.Name,
            Module = p.Module,
            Action = p.Action,
            Description = p.Description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        }).ToList();

        await _context.Permissions.AddRangeAsync(permissionEntities);
        _logger.LogInformation($"Added {permissionEntities.Count} permissions.");
    }

    private async Task SeedRolesAsync()
    {
        if (await _context.Roles.AnyAsync())
        {
            _logger.LogInformation("Roles already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding roles...");

        var defaultRoles = Roles.GetDefaultRoles();
        var roleEntities = defaultRoles.Select(r => new Role
        {
            Id = Guid.NewGuid(),
            Name = r.Name,
            Description = r.Description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        }).ToList();

        await _context.Roles.AddRangeAsync(roleEntities);
        _logger.LogInformation($"Added {roleEntities.Count} roles.");
    }

    private async Task SeedRolePermissionsAsync()
    {
        if (await _context.RolePermissions.AnyAsync())
        {
            _logger.LogInformation("Role permissions already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding role permissions...");

        var roles = await _context.Roles.ToListAsync();
        var permissions = await _context.Permissions.ToListAsync();
        var defaultRoles = Roles.GetDefaultRoles();

        var rolePermissions = new List<RolePermission>();

        foreach (var defaultRole in defaultRoles)
        {
            var role = roles.FirstOrDefault(r => r.Name == defaultRole.Name);
            if (role == null) continue;

            foreach (var permissionName in defaultRole.Permissions)
            {
                var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                if (permission == null) continue;

                rolePermissions.Add(new RolePermission
                {
                    Id = Guid.NewGuid(),
                    RoleId = role.Id,
                    PermissionId = permission.Id,
                    GrantedDate = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow
                });
            }
        }

        await _context.RolePermissions.AddRangeAsync(rolePermissions);
        _logger.LogInformation($"Added {rolePermissions.Count} role permissions.");
    }

    private async Task SeedUsersAsync()
    {
        if (await _context.Users.AnyAsync())
        {
            _logger.LogInformation("Users already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding default users...");

        var superAdminRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == Roles.SuperAdmin);
        var adminRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == Roles.Admin);

        if (superAdminRole == null || adminRole == null)
        {
            _logger.LogWarning("Required roles not found, skipping user seeding.");
            return;
        }

        // Create SuperAdmin user
        var superAdminUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "superadmin",
            Email = "<EMAIL>",
            PasswordHash = HashPassword("SuperAdmin123!"),
            FirstName = "Super",
            LastName = "Administrator",
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };

        // Create Admin user
        var adminUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "admin",
            Email = "<EMAIL>",
            PasswordHash = HashPassword("Admin123!"),
            FirstName = "System",
            LastName = "Administrator",
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };

        await _context.Users.AddRangeAsync(superAdminUser, adminUser);

        // Assign roles
        var userRoles = new List<UserRole>
        {
            new UserRole
            {
                Id = Guid.NewGuid(),
                UserId = superAdminUser.Id,
                RoleId = superAdminRole.Id,
                AssignedDate = DateTime.UtcNow,
                CreatedDate = DateTime.UtcNow
            },
            new UserRole
            {
                Id = Guid.NewGuid(),
                UserId = adminUser.Id,
                RoleId = adminRole.Id,
                AssignedDate = DateTime.UtcNow,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _context.UserRoles.AddRangeAsync(userRoles);
        _logger.LogInformation("Added default users with roles.");
    }

    private async Task SeedSystemSettingsAsync()
    {
        if (await _context.SystemSettings.AnyAsync())
        {
            _logger.LogInformation("System settings already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding system settings...");

        var settings = new List<SystemSetting>
        {
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyName",
                SettingValue = "Armored Doors Manufacturing Company",
                SettingType = "String",
                Description = "Company name displayed in the system",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyAddress",
                SettingValue = "123 Industrial Street, Manufacturing District",
                SettingType = "String",
                Description = "Company address for reports and documents",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyPhone",
                SettingValue = "******-0123",
                SettingType = "String",
                Description = "Company phone number",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyEmail",
                SettingValue = "<EMAIL>",
                SettingType = "String",
                Description = "Company email address",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "DefaultCurrency",
                SettingValue = "USD",
                SettingType = "String",
                Description = "Default currency for financial transactions",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "LowStockThreshold",
                SettingValue = "10",
                SettingType = "Number",
                Description = "Default low stock threshold percentage",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "AutoGenerateBarcodes",
                SettingValue = "true",
                SettingType = "Boolean",
                Description = "Automatically generate barcodes for new products",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _context.SystemSettings.AddRangeAsync(settings);
        _logger.LogInformation($"Added {settings.Count} system settings.");
    }

    private async Task SeedMeasurementUnitsAsync()
    {
        if (await _context.MeasurementUnits.AnyAsync())
        {
            _logger.LogInformation("Measurement units already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding measurement units...");

        var units = new List<MeasurementUnit>
        {
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Piece", Symbol = "pcs", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Kilogram", Symbol = "kg", UnitType = "Weight", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Gram", Symbol = "g", UnitType = "Weight", BaseUnit = "Kilogram", ConversionFactor = 0.001m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Meter", Symbol = "m", UnitType = "Length", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Centimeter", Symbol = "cm", UnitType = "Length", BaseUnit = "Meter", ConversionFactor = 0.01m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Liter", Symbol = "L", UnitType = "Volume", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Square Meter", Symbol = "m²", UnitType = "Area", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Box", Symbol = "box", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Package", Symbol = "pkg", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.MeasurementUnits.AddRangeAsync(units);
        _logger.LogInformation($"Added {units.Count} measurement units.");
    }

    private async Task SeedCategoriesAsync()
    {
        if (await _context.Categories.AnyAsync())
        {
            _logger.LogInformation("Categories already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding categories...");

        var categories = new List<Category>
        {
            // Raw Material Categories
            new Category { Id = Guid.NewGuid(), Name = "Steel", Description = "Steel materials and components", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Hardware", Description = "Locks, hinges, and other hardware", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Glass", Description = "Security glass and panels", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Insulation", Description = "Insulation materials", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Finishing", Description = "Paint, coating, and finishing materials", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Finished Product Categories
            new Category { Id = Guid.NewGuid(), Name = "Residential Doors", Description = "Armored doors for residential use", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Commercial Doors", Description = "Armored doors for commercial use", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Security Doors", Description = "High-security armored doors", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Fire Doors", Description = "Fire-resistant armored doors", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Custom Doors", Description = "Custom-made armored doors", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.Categories.AddRangeAsync(categories);
        _logger.LogInformation($"Added {categories.Count} categories.");
    }

    private async Task SeedStorageLocationsAsync()
    {
        if (await _context.StorageLocations.AnyAsync())
        {
            _logger.LogInformation("Storage locations already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding storage locations...");

        var locations = new List<StorageLocation>
        {
            new StorageLocation { Id = Guid.NewGuid(), Code = "WH-01", Name = "Main Warehouse", Description = "Primary warehouse facility", LocationType = "Warehouse", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "WH-02", Name = "Finished Goods Warehouse", Description = "Warehouse for finished products", LocationType = "Warehouse", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-A", Name = "Section A", Description = "Raw materials section", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-B", Name = "Section B", Description = "Hardware section", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-C", Name = "Section C", Description = "Finished products section", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.StorageLocations.AddRangeAsync(locations);
        _logger.LogInformation($"Added {locations.Count} storage locations.");
    }

    private async Task SeedFinancialAccountsAsync()
    {
        if (await _context.FinancialAccounts.AnyAsync())
        {
            _logger.LogInformation("Financial accounts already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding financial accounts...");

        var accounts = new List<FinancialAccount>
        {
            // Assets
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1000", AccountName = "Cash", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1100", AccountName = "Accounts Receivable", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1200", AccountName = "Inventory - Raw Materials", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1300", AccountName = "Inventory - Finished Goods", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Liabilities
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "2000", AccountName = "Accounts Payable", AccountType = "Liability", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "2100", AccountName = "Accrued Expenses", AccountType = "Liability", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Equity
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "3000", AccountName = "Partner Capital", AccountType = "Equity", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "3100", AccountName = "Retained Earnings", AccountType = "Equity", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Revenue
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "4000", AccountName = "Sales Revenue", AccountType = "Revenue", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Expenses
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "5000", AccountName = "Cost of Goods Sold", AccountType = "Expense", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "5100", AccountName = "Operating Expenses", AccountType = "Expense", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.FinancialAccounts.AddRangeAsync(accounts);
        _logger.LogInformation($"Added {accounts.Count} financial accounts.");
    }

    private static string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ArmoredDoorsSalt"));
        return Convert.ToBase64String(hashedBytes);
    }
}
