using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Constants;
using System.Security.Cryptography;
using System.Text;

namespace ArmoredDoors.Infrastructure.Data.SeedData;

public class DatabaseSeeder
{
    private readonly ArmoredDoorsDbContext _context;
    private readonly ILogger<DatabaseSeeder> _logger;

    public DatabaseSeeder(ArmoredDoorsDbContext context, ILogger<DatabaseSeeder> logger)
    {
        _context = context;
        _logger = logger;
    }

    public async Task SeedAsync()
    {
        try
        {
            _logger.LogInformation("Starting database seeding...");

            // Ensure database is created
            await _context.Database.EnsureCreatedAsync();

            // Seed in order of dependencies
            await SeedPermissionsAsync();
            await SeedRolesAsync();
            await SeedRolePermissionsAsync();
            await SeedUsersAsync();
            await SeedSystemSettingsAsync();
            await SeedMeasurementUnitsAsync();
            await SeedCategoriesAsync();
            await SeedStorageLocationsAsync();
            await SeedFinancialAccountsAsync();
            await SeedSampleCustomersAsync();
            await SeedSampleSuppliersAsync();

            await _context.SaveChangesAsync();
            _logger.LogInformation("Database seeding completed successfully.");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "An error occurred while seeding the database.");
            throw;
        }
    }

    private async Task SeedPermissionsAsync()
    {
        if (await _context.Permissions.AnyAsync())
        {
            _logger.LogInformation("Permissions already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding permissions...");

        var permissions = Permissions.GetAllPermissions();
        var permissionEntities = permissions.Select(p => new Permission
        {
            Id = Guid.NewGuid(),
            Name = p.Name,
            Module = p.Module,
            Action = p.Action,
            Description = p.Description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        }).ToList();

        await _context.Permissions.AddRangeAsync(permissionEntities);
        _logger.LogInformation($"Added {permissionEntities.Count} permissions.");
    }

    private async Task SeedRolesAsync()
    {
        if (await _context.Roles.AnyAsync())
        {
            _logger.LogInformation("Roles already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding roles...");

        var defaultRoles = Roles.GetDefaultRoles();
        var roleEntities = defaultRoles.Select(r => new Role
        {
            Id = Guid.NewGuid(),
            Name = r.Name,
            Description = r.Description,
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        }).ToList();

        await _context.Roles.AddRangeAsync(roleEntities);
        _logger.LogInformation($"Added {roleEntities.Count} roles.");
    }

    private async Task SeedRolePermissionsAsync()
    {
        if (await _context.RolePermissions.AnyAsync())
        {
            _logger.LogInformation("Role permissions already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding role permissions...");

        var roles = await _context.Roles.ToListAsync();
        var permissions = await _context.Permissions.ToListAsync();
        var defaultRoles = Roles.GetDefaultRoles();

        var rolePermissions = new List<RolePermission>();

        foreach (var defaultRole in defaultRoles)
        {
            var role = roles.FirstOrDefault(r => r.Name == defaultRole.Name);
            if (role == null) continue;

            foreach (var permissionName in defaultRole.Permissions)
            {
                var permission = permissions.FirstOrDefault(p => p.Name == permissionName);
                if (permission == null) continue;

                rolePermissions.Add(new RolePermission
                {
                    Id = Guid.NewGuid(),
                    RoleId = role.Id,
                    PermissionId = permission.Id,
                    GrantedDate = DateTime.UtcNow,
                    CreatedDate = DateTime.UtcNow
                });
            }
        }

        await _context.RolePermissions.AddRangeAsync(rolePermissions);
        _logger.LogInformation($"Added {rolePermissions.Count} role permissions.");
    }

    private async Task SeedUsersAsync()
    {
        if (await _context.Users.AnyAsync())
        {
            _logger.LogInformation("Users already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding default users...");

        var superAdminRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == Roles.SuperAdmin);
        var adminRole = await _context.Roles.FirstOrDefaultAsync(r => r.Name == Roles.Admin);

        if (superAdminRole == null || adminRole == null)
        {
            _logger.LogWarning("Required roles not found, skipping user seeding.");
            return;
        }

        // Create SuperAdmin user
        var superAdminUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "superadmin",
            Email = "<EMAIL>",
            PasswordHash = HashPassword("SuperAdmin123!"),
            FirstName = "Super",
            LastName = "Administrator",
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };

        // Create Admin user
        var adminUser = new User
        {
            Id = Guid.NewGuid(),
            Username = "admin",
            Email = "<EMAIL>",
            PasswordHash = HashPassword("Admin123!"),
            FirstName = "System",
            LastName = "Administrator",
            IsActive = true,
            CreatedDate = DateTime.UtcNow
        };

        await _context.Users.AddRangeAsync(superAdminUser, adminUser);

        // Assign roles
        var userRoles = new List<UserRole>
        {
            new UserRole
            {
                Id = Guid.NewGuid(),
                UserId = superAdminUser.Id,
                RoleId = superAdminRole.Id,
                AssignedDate = DateTime.UtcNow,
                CreatedDate = DateTime.UtcNow
            },
            new UserRole
            {
                Id = Guid.NewGuid(),
                UserId = adminUser.Id,
                RoleId = adminRole.Id,
                AssignedDate = DateTime.UtcNow,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _context.UserRoles.AddRangeAsync(userRoles);
        _logger.LogInformation("Added default users with roles.");
    }

    private async Task SeedSystemSettingsAsync()
    {
        if (await _context.SystemSettings.AnyAsync())
        {
            _logger.LogInformation("System settings already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding system settings...");

        var settings = new List<SystemSetting>
        {
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyName",
                SettingValue = "شركة تصنيع الأبواب المدرعة",
                SettingType = "String",
                Description = "Company name displayed in the system",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyNameEn",
                SettingValue = "Armored Doors Manufacturing Company",
                SettingType = "String",
                Description = "Company name in English",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyAddress",
                SettingValue = "شارع الصناعة 123، المنطقة الصناعية، القاهرة، مصر",
                SettingType = "String",
                Description = "Company address for reports and documents",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyAddressEn",
                SettingValue = "123 Industrial Street, Industrial Zone, Cairo, Egypt",
                SettingType = "String",
                Description = "Company address in English",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyPhone",
                SettingValue = "+20-2-1234-5678",
                SettingType = "String",
                Description = "Company phone number",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CompanyEmail",
                SettingValue = "<EMAIL>",
                SettingType = "String",
                Description = "Company email address",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "DefaultCurrency",
                SettingValue = "EGP",
                SettingType = "String",
                Description = "Default currency for financial transactions (Egyptian Pound)",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "DefaultLanguage",
                SettingValue = "ar-EG",
                SettingType = "String",
                Description = "Default system language (Arabic - Egypt)",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "TaxNumber",
                SettingValue = "***********",
                SettingType = "String",
                Description = "Company tax registration number",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "CommercialRegister",
                SettingValue = "*********",
                SettingType = "String",
                Description = "Commercial register number",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "LowStockThreshold",
                SettingValue = "10",
                SettingType = "Number",
                Description = "Default low stock threshold percentage",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            },
            new SystemSetting
            {
                Id = Guid.NewGuid(),
                SettingKey = "AutoGenerateBarcodes",
                SettingValue = "true",
                SettingType = "Boolean",
                Description = "Automatically generate barcodes for new products",
                IsEditable = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _context.SystemSettings.AddRangeAsync(settings);
        _logger.LogInformation($"Added {settings.Count} system settings.");
    }

    private async Task SeedMeasurementUnitsAsync()
    {
        if (await _context.MeasurementUnits.AnyAsync())
        {
            _logger.LogInformation("Measurement units already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding measurement units...");

        var units = new List<MeasurementUnit>
        {
            // Count Units
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "قطعة", Symbol = "قطعة", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Piece", Symbol = "pcs", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "صندوق", Symbol = "صندوق", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Box", Symbol = "box", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "عبوة", Symbol = "عبوة", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Package", Symbol = "pkg", UnitType = "Count", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Weight Units
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "كيلوجرام", Symbol = "كجم", UnitType = "Weight", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Kilogram", Symbol = "kg", UnitType = "Weight", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "جرام", Symbol = "جم", UnitType = "Weight", BaseUnit = "كيلوجرام", ConversionFactor = 0.001m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Gram", Symbol = "g", UnitType = "Weight", BaseUnit = "Kilogram", ConversionFactor = 0.001m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "طن", Symbol = "طن", UnitType = "Weight", BaseUnit = "كيلوجرام", ConversionFactor = 1000m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Ton", Symbol = "t", UnitType = "Weight", BaseUnit = "Kilogram", ConversionFactor = 1000m, IsActive = true, CreatedDate = DateTime.UtcNow },

            // Length Units
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "متر", Symbol = "م", UnitType = "Length", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Meter", Symbol = "m", UnitType = "Length", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "سنتيمتر", Symbol = "سم", UnitType = "Length", BaseUnit = "متر", ConversionFactor = 0.01m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Centimeter", Symbol = "cm", UnitType = "Length", BaseUnit = "Meter", ConversionFactor = 0.01m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "مليمتر", Symbol = "مم", UnitType = "Length", BaseUnit = "متر", ConversionFactor = 0.001m, IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Millimeter", Symbol = "mm", UnitType = "Length", BaseUnit = "Meter", ConversionFactor = 0.001m, IsActive = true, CreatedDate = DateTime.UtcNow },

            // Volume Units
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "لتر", Symbol = "لتر", UnitType = "Volume", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Liter", Symbol = "L", UnitType = "Volume", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Area Units
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "متر مربع", Symbol = "م²", UnitType = "Area", IsActive = true, CreatedDate = DateTime.UtcNow },
            new MeasurementUnit { Id = Guid.NewGuid(), Name = "Square Meter", Symbol = "m²", UnitType = "Area", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.MeasurementUnits.AddRangeAsync(units);
        _logger.LogInformation($"Added {units.Count} measurement units.");
    }

    private async Task SeedCategoriesAsync()
    {
        if (await _context.Categories.AnyAsync())
        {
            _logger.LogInformation("Categories already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding categories...");

        var categories = new List<Category>
        {
            // Raw Material Categories (Arabic)
            new Category { Id = Guid.NewGuid(), Name = "الصلب والحديد", Description = "مواد الصلب والحديد والمكونات المعدنية", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "الأقفال والمفصلات", Description = "الأقفال والمفصلات والأجهزة المعدنية", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "الزجاج الأمني", Description = "الزجاج الأمني والألواح المقاومة", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "مواد العزل", Description = "مواد العزل الحراري والصوتي", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "مواد التشطيب", Description = "الدهانات والطلاءات ومواد التشطيب", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "المطاط والبلاستيك", Description = "مواد المطاط والبلاستيك والعوازل", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Raw Material Categories (English)
            new Category { Id = Guid.NewGuid(), Name = "Steel & Iron", Description = "Steel materials and iron components", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Hardware", Description = "Locks, hinges, and other hardware", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Security Glass", Description = "Security glass and resistant panels", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Insulation", Description = "Thermal and acoustic insulation materials", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Finishing Materials", Description = "Paint, coating, and finishing materials", CategoryType = "RawMaterial", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Finished Product Categories (Arabic)
            new Category { Id = Guid.NewGuid(), Name = "الأبواب السكنية", Description = "الأبواب المدرعة للاستخدام السكني", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "الأبواب التجارية", Description = "الأبواب المدرعة للاستخدام التجاري", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "الأبواب الأمنية", Description = "الأبواب المدرعة عالية الأمان", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "أبواب مقاومة الحريق", Description = "الأبواب المدرعة المقاومة للحريق", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "الأبواب المخصصة", Description = "الأبواب المدرعة المصنوعة حسب الطلب", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Finished Product Categories (English)
            new Category { Id = Guid.NewGuid(), Name = "Residential Doors", Description = "Armored doors for residential use", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Commercial Doors", Description = "Armored doors for commercial use", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Security Doors", Description = "High-security armored doors", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Fire Doors", Description = "Fire-resistant armored doors", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow },
            new Category { Id = Guid.NewGuid(), Name = "Custom Doors", Description = "Custom-made armored doors", CategoryType = "FinishedProduct", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.Categories.AddRangeAsync(categories);
        _logger.LogInformation($"Added {categories.Count} categories.");
    }

    private async Task SeedStorageLocationsAsync()
    {
        if (await _context.StorageLocations.AnyAsync())
        {
            _logger.LogInformation("Storage locations already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding storage locations...");

        var locations = new List<StorageLocation>
        {
            // Main Warehouses
            new StorageLocation { Id = Guid.NewGuid(), Code = "WH-01", Name = "المستودع الرئيسي", Description = "المستودع الرئيسي للمواد الخام", LocationType = "Warehouse", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "WH-02", Name = "مستودع المنتجات النهائية", Description = "مستودع المنتجات النهائية والأبواب المكتملة", LocationType = "Warehouse", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "WH-03", Name = "مستودع قطع الغيار", Description = "مستودع قطع الغيار والأجهزة", LocationType = "Warehouse", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Sections
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-A", Name = "قسم المواد الخام", Description = "قسم تخزين المواد الخام والصلب", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-B", Name = "قسم الأقفال والمفصلات", Description = "قسم تخزين الأقفال والمفصلات", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-C", Name = "قسم المنتجات النهائية", Description = "قسم تخزين المنتجات النهائية", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-D", Name = "قسم الزجاج الأمني", Description = "قسم تخزين الزجاج الأمني والألواح", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SEC-E", Name = "قسم مواد التشطيب", Description = "قسم تخزين الدهانات ومواد التشطيب", LocationType = "Section", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Shelves
            new StorageLocation { Id = Guid.NewGuid(), Code = "SH-A1", Name = "رف أ-1", Description = "رف تخزين المواد الصغيرة", LocationType = "Shelf", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SH-A2", Name = "رف أ-2", Description = "رف تخزين الأقفال", LocationType = "Shelf", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SH-B1", Name = "رف ب-1", Description = "رف تخزين المفصلات", LocationType = "Shelf", IsActive = true, CreatedDate = DateTime.UtcNow },
            new StorageLocation { Id = Guid.NewGuid(), Code = "SH-B2", Name = "رف ب-2", Description = "رف تخزين الأدوات", LocationType = "Shelf", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.StorageLocations.AddRangeAsync(locations);
        _logger.LogInformation($"Added {locations.Count} storage locations.");
    }

    private async Task SeedFinancialAccountsAsync()
    {
        if (await _context.FinancialAccounts.AnyAsync())
        {
            _logger.LogInformation("Financial accounts already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding financial accounts...");

        var accounts = new List<FinancialAccount>
        {
            // Assets
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1000", AccountName = "Cash", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1100", AccountName = "Accounts Receivable", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1200", AccountName = "Inventory - Raw Materials", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "1300", AccountName = "Inventory - Finished Goods", AccountType = "Asset", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Liabilities
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "2000", AccountName = "Accounts Payable", AccountType = "Liability", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "2100", AccountName = "Accrued Expenses", AccountType = "Liability", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Equity
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "3000", AccountName = "Partner Capital", AccountType = "Equity", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "3100", AccountName = "Retained Earnings", AccountType = "Equity", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Revenue
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "4000", AccountName = "Sales Revenue", AccountType = "Revenue", IsActive = true, CreatedDate = DateTime.UtcNow },

            // Expenses
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "5000", AccountName = "Cost of Goods Sold", AccountType = "Expense", IsActive = true, CreatedDate = DateTime.UtcNow },
            new FinancialAccount { Id = Guid.NewGuid(), AccountCode = "5100", AccountName = "Operating Expenses", AccountType = "Expense", IsActive = true, CreatedDate = DateTime.UtcNow }
        };

        await _context.FinancialAccounts.AddRangeAsync(accounts);
        _logger.LogInformation($"Added {accounts.Count} financial accounts.");
    }

    private async Task SeedSampleCustomersAsync()
    {
        if (await _context.Customers.AnyAsync())
        {
            _logger.LogInformation("Customers already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding sample customers...");

        var customers = new List<Customer>
        {
            new Customer
            {
                Id = Guid.NewGuid(),
                Code = "CUST-001",
                Name = "شركة الأهرام للمقاولات",
                ContactPerson = "أحمد محمد علي",
                Email = "<EMAIL>",
                PhoneNumber = "+20-2-2345-6789",
                Address = "شارع النيل 45، المعادي، القاهرة",
                City = "القاهرة",
                Country = "مصر",
                TaxNumber = "***********",
                CreditLimit = 100000,
                PaymentTerms = 30,
                CustomerType = "Distributor",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Customer
            {
                Id = Guid.NewGuid(),
                Code = "CUST-002",
                Name = "مجموعة النصر للتطوير العقاري",
                ContactPerson = "فاطمة أحمد حسن",
                Email = "<EMAIL>",
                PhoneNumber = "+20-2-3456-7890",
                Address = "شارع التحرير 123، وسط البلد، القاهرة",
                City = "القاهرة",
                Country = "مصر",
                TaxNumber = "***********",
                CreditLimit = 250000,
                PaymentTerms = 45,
                CustomerType = "Wholesale",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Customer
            {
                Id = Guid.NewGuid(),
                Code = "CUST-003",
                Name = "محمد عبد الرحمن للأمن والحماية",
                ContactPerson = "محمد عبد الرحمن",
                Email = "<EMAIL>",
                PhoneNumber = "+20-10-1234-5678",
                Address = "شارع الجمهورية 67، الإسكندرية",
                City = "الإسكندرية",
                Country = "مصر",
                TaxNumber = "***********",
                CreditLimit = 75000,
                PaymentTerms = 30,
                CustomerType = "Retail",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Customer
            {
                Id = Guid.NewGuid(),
                Code = "CUST-004",
                Name = "شركة الدلتا للإنشاءات",
                ContactPerson = "سارة محمود إبراهيم",
                Email = "<EMAIL>",
                PhoneNumber = "+20-50-9876-5432",
                Address = "شارع الزراعة 89، طنطا، الغربية",
                City = "طنطا",
                Country = "مصر",
                TaxNumber = "***********",
                CreditLimit = 150000,
                PaymentTerms = 60,
                CustomerType = "Distributor",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _context.Customers.AddRangeAsync(customers);
        _logger.LogInformation($"Added {customers.Count} sample customers.");
    }

    private async Task SeedSampleSuppliersAsync()
    {
        if (await _context.Suppliers.AnyAsync())
        {
            _logger.LogInformation("Suppliers already exist, skipping seed.");
            return;
        }

        _logger.LogInformation("Seeding sample suppliers...");

        var suppliers = new List<Supplier>
        {
            new Supplier
            {
                Id = Guid.NewGuid(),
                Code = "SUPP-001",
                Name = "شركة الحديد والصلب المصرية",
                ContactPerson = "خالد أحمد محمد",
                Email = "<EMAIL>",
                PhoneNumber = "+20-2-4567-8901",
                Address = "المنطقة الصناعية، حلوان، القاهرة",
                City = "القاهرة",
                Country = "مصر",
                TaxNumber = "***********",
                PaymentTerms = 30,
                SupplierType = "Material",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Supplier
            {
                Id = Guid.NewGuid(),
                Code = "SUPP-002",
                Name = "مصنع الأقفال الحديثة",
                ContactPerson = "نادية حسن علي",
                Email = "<EMAIL>",
                PhoneNumber = "+20-3-5678-9012",
                Address = "شارع الصناعة 234، برج العرب، الإسكندرية",
                City = "الإسكندرية",
                Country = "مصر",
                TaxNumber = "***********",
                PaymentTerms = 45,
                SupplierType = "Material",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Supplier
            {
                Id = Guid.NewGuid(),
                Code = "SUPP-003",
                Name = "شركة الزجاج الأمني المتطور",
                ContactPerson = "عمر محمد حسين",
                Email = "<EMAIL>",
                PhoneNumber = "+20-2-6789-0123",
                Address = "شارع الإنتاج 156، العبور، القليوبية",
                City = "العبور",
                Country = "مصر",
                TaxNumber = "***********",
                PaymentTerms = 30,
                SupplierType = "Material",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Supplier
            {
                Id = Guid.NewGuid(),
                Code = "SUPP-004",
                Name = "مؤسسة الدهانات والطلاءات",
                ContactPerson = "ليلى عبد الله أحمد",
                Email = "<EMAIL>",
                PhoneNumber = "+20-2-7890-1234",
                Address = "شارع التجارة 78، شبرا الخيمة، القليوبية",
                City = "شبرا الخيمة",
                Country = "مصر",
                TaxNumber = "***********",
                PaymentTerms = 30,
                SupplierType = "Material",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            },
            new Supplier
            {
                Id = Guid.NewGuid(),
                Code = "SUPP-005",
                Name = "شركة النقل والخدمات اللوجستية",
                ContactPerson = "حسام الدين محمد",
                Email = "<EMAIL>",
                PhoneNumber = "+20-2-8901-2345",
                Address = "شارع النقل 45، مدينة نصر، القاهرة",
                City = "القاهرة",
                Country = "مصر",
                TaxNumber = "***********",
                PaymentTerms = 15,
                SupplierType = "Service",
                IsActive = true,
                CreatedDate = DateTime.UtcNow
            }
        };

        await _context.Suppliers.AddRangeAsync(suppliers);
        _logger.LogInformation($"Added {suppliers.Count} sample suppliers.");
    }

    private static string HashPassword(string password)
    {
        using var sha256 = SHA256.Create();
        var hashedBytes = sha256.ComputeHash(Encoding.UTF8.GetBytes(password + "ArmoredDoorsSalt"));
        return Convert.ToBase64String(hashedBytes);
    }
}
