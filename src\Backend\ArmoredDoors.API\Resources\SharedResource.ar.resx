<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" msdata:Ordinal="5" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="InvalidCredentials" xml:space="preserve">
    <value>اسم المستخدم أو كلمة المرور غير صحيحة</value>
  </data>
  <data name="LoginSuccessful" xml:space="preserve">
    <value>تم تسجيل الدخول بنجاح</value>
  </data>
  <data name="TokenRefreshed" xml:space="preserve">
    <value>تم تحديث الرمز المميز بنجاح</value>
  </data>
  <data name="TokenRevoked" xml:space="preserve">
    <value>تم إلغاء الرمز المميز بنجاح</value>
  </data>
  
  <!-- User Management -->
  <data name="UserCreated" xml:space="preserve">
    <value>تم إنشاء المستخدم بنجاح</value>
  </data>
  <data name="UserUpdated" xml:space="preserve">
    <value>تم تحديث المستخدم بنجاح</value>
  </data>
  <data name="UserDeleted" xml:space="preserve">
    <value>تم حذف المستخدم بنجاح</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>المستخدم غير موجود</value>
  </data>
  <data name="UsernameExists" xml:space="preserve">
    <value>اسم المستخدم موجود بالفعل</value>
  </data>
  <data name="EmailExists" xml:space="preserve">
    <value>البريد الإلكتروني موجود بالفعل</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="RequiredField" xml:space="preserve">
    <value>{0} مطلوب</value>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>تنسيق البريد الإلكتروني غير صحيح</value>
  </data>
  <data name="InvalidPhoneNumber" xml:space="preserve">
    <value>تنسيق رقم الهاتف غير صحيح</value>
  </data>
  <data name="PasswordTooShort" xml:space="preserve">
    <value>يجب أن تكون كلمة المرور {0} أحرف على الأقل</value>
  </data>
  
  <!-- General Messages -->
  <data name="Success" xml:space="preserve">
    <value>تمت العملية بنجاح</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>حدث خطأ</value>
  </data>
  <data name="InsufficientPermissions" xml:space="preserve">
    <value>صلاحيات غير كافية لتنفيذ هذا الإجراء</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>المورد غير موجود</value>
  </data>
  
  <!-- Company Information -->
  <data name="CompanyName" xml:space="preserve">
    <value>شركة تصنيع الأبواب المدرعة</value>
  </data>
  <data name="CompanyAddress" xml:space="preserve">
    <value>شارع الصناعة 123، المنطقة الصناعية، القاهرة، مصر</value>
  </data>
  <data name="CompanyPhone" xml:space="preserve">
    <value>+20-2-1234-5678</value>
  </data>
  <data name="CompanyEmail" xml:space="preserve">
    <value><EMAIL></value>
  </data>
</root>
