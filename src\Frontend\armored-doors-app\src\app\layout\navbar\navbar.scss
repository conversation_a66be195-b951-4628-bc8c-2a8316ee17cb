// Navbar Styles with distinctive dark mode design
.navbar {
  @apply sticky top-0 z-30 shadow-sm transition-all duration-300;

  // Light mode: Clean white with subtle border
  @apply bg-white border-b border-gray-200;

  // Dark mode: Distinctive blue gradient background
  @apply dark:bg-gradient-to-r dark:from-blue-900 dark:to-blue-800 dark:border-blue-700;

  // Enhanced shadow in dark mode
  @apply dark:shadow-lg dark:shadow-blue-900/20;
}

.navbar-container {
  @apply flex items-center justify-between px-4 py-3;

  // Responsive padding
  @media (max-width: 767px) {
    @apply px-3 py-2;
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    @apply px-5 py-3;
  }

  @media (min-width: 1024px) {
    @apply px-6 py-4;
  }
}

// Left side
.navbar-left {
  @apply flex items-center space-x-4;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }
}

.menu-toggle-btn {
  @apply p-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50;

  // Light mode
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-primary-500;

  // Dark mode: Enhanced visibility on blue background
  @apply dark:text-blue-100 dark:hover:text-white dark:hover:bg-blue-800/50 dark:focus:ring-blue-300;
}

.breadcrumbs {
  @apply hidden sm:block;

  span {
    @apply font-medium transition-colors duration-200;

    // Light mode
    @apply text-gray-600;

    // Dark mode: Enhanced contrast on blue background
    @apply dark:text-blue-100;

    // Arabic text enhancement
    [dir="rtl"] & {
      @apply font-semibold;
    }
  }
}

// Right side
.navbar-right {
  @apply flex items-center space-x-4;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }

  // Responsive spacing
  @media (max-width: 767px) {
    @apply space-x-2;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }
  }
}

// Notification button
.notification-btn {
  @apply relative p-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50;

  // Light mode
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100 focus:ring-primary-500;

  // Dark mode: Enhanced visibility on blue background
  @apply dark:text-blue-100 dark:hover:text-white dark:hover:bg-blue-800/50 dark:focus:ring-blue-300;
}

.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium;

  [dir="rtl"] & {
    @apply right-auto left-1;
  }
}

// User menu
.user-menu {
  @apply relative;
}

.user-menu-btn {
  @apply flex items-center space-x-3 p-2 rounded-lg transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-opacity-50;

  // Light mode
  @apply text-gray-700 hover:bg-gray-100 focus:ring-primary-500;

  // Dark mode: Enhanced visibility on blue background
  @apply dark:text-blue-100 dark:hover:text-white dark:hover:bg-blue-800/50 dark:focus:ring-blue-300;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply space-x-2 p-1;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }
  }
}

.user-avatar {
  @apply w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0;

  .avatar-text {
    @apply text-white text-sm font-medium;
  }
}

.user-info {
  @apply flex flex-col items-start;

  [dir="rtl"] & {
    @apply items-end;
  }

  .user-name {
    @apply text-sm font-medium leading-tight transition-colors duration-200;

    // Light mode
    @apply text-gray-900;

    // Dark mode: Enhanced contrast on blue background
    @apply dark:text-white;

    // Arabic text enhancement
    [dir="rtl"] & {
      @apply font-semibold;
    }
  }

  .user-role {
    @apply text-xs transition-colors duration-200;

    // Light mode
    @apply text-gray-500;

    // Dark mode: Enhanced contrast on blue background
    @apply dark:text-blue-200;

    // Arabic text enhancement
    [dir="rtl"] & {
      @apply font-medium;
    }
  }
}

// User dropdown
.user-dropdown {
  @apply absolute right-0 mt-2 w-48 rounded-lg shadow-lg py-1 z-50 transition-colors duration-200;

  // Light mode
  @apply bg-white border border-gray-200;

  // Dark mode
  @apply dark:bg-gray-800 dark:border-gray-700;

  [dir="rtl"] & {
    @apply right-auto left-0;
  }

  &.hidden {
    display: none;
  }
}

.dropdown-item {
  @apply flex items-center space-x-3 px-4 py-2 text-sm transition-colors duration-150;

  // Light mode
  @apply text-gray-700 hover:bg-gray-100;

  // Dark mode
  @apply dark:text-gray-200 dark:hover:bg-gray-700;

  [dir="rtl"] & {
    @apply space-x-reverse;

    // Arabic text enhancement
    @apply font-medium;
  }

  svg {
    @apply flex-shrink-0;
  }

  &.error-text {
    @apply text-red-600 dark:text-red-400 hover:bg-red-50 dark:hover:bg-red-900/20;
  }
}


.dropdown-divider {
  @apply border-t border-gray-200 my-1;
}

// Responsive adjustments
@media (max-width: 767px) {
  .navbar {
    @apply text-sm;
  }

  .user-info {
    @apply hidden;
  }

  .breadcrumbs {
    @apply hidden;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .navbar {
    @apply text-sm;
  }
}

// Animation classes
.dropdown-enter {
  @apply opacity-0 scale-95;
}

.dropdown-enter-active {
  @apply transition-all duration-200 ease-out;
}

.dropdown-enter-to {
  @apply opacity-100 scale-100;
}

.dropdown-leave {
  @apply opacity-100 scale-100;
}

.dropdown-leave-active {
  @apply transition-all duration-150 ease-in;
}

.dropdown-leave-to {
  @apply opacity-0 scale-95;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .menu-toggle-btn,
  .notification-btn,
  .user-menu-btn,
  .dropdown-item {
    @apply transition-none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .navbar {
    @apply border-2 border-gray-900;
  }

  .menu-toggle-btn,
  .notification-btn,
  .user-menu-btn {
    @apply border border-gray-300;

    &:hover {
      @apply border-gray-900;
    }
  }

  .user-dropdown {
    @apply border-2 border-gray-900;
  }
}