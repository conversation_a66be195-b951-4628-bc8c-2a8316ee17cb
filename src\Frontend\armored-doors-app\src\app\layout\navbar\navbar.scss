// Navbar Styles
.navbar {
  @apply bg-white border-b border-gray-200 shadow-sm sticky top-0 z-30;
}

.navbar-container {
  @apply flex items-center justify-between px-4 py-3;

  // Responsive padding
  @media (max-width: 767px) {
    @apply px-3 py-2;
  }

  @media (min-width: 768px) and (max-width: 1023px) {
    @apply px-5 py-3;
  }

  @media (min-width: 1024px) {
    @apply px-6 py-4;
  }
}

// Left side
.navbar-left {
  @apply flex items-center space-x-4;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }
}

.menu-toggle-btn {
  @apply p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
}

.breadcrumbs {
  @apply hidden sm:block;

  span {
    @apply font-medium;
  }
}

// Right side
.navbar-right {
  @apply flex items-center space-x-4;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }

  // Responsive spacing
  @media (max-width: 767px) {
    @apply space-x-2;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }
  }
}

// Notification button
.notification-btn {
  @apply relative p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;
}

.notification-badge {
  @apply absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center font-medium;

  [dir="rtl"] & {
    @apply right-auto left-1;
  }
}

// User menu
.user-menu {
  @apply relative;
}

.user-menu-btn {
  @apply flex items-center space-x-3 p-2 rounded-lg text-gray-700 hover:bg-gray-100 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }

  // Mobile adjustments
  @media (max-width: 767px) {
    @apply space-x-2 p-1;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }
  }
}

.user-avatar {
  @apply w-8 h-8 bg-primary-600 rounded-full flex items-center justify-center flex-shrink-0;

  .avatar-text {
    @apply text-white text-sm font-medium;
  }
}

.user-info {
  @apply flex flex-col items-start;

  [dir="rtl"] & {
    @apply items-end;
  }

  .user-name {
    @apply text-sm font-medium text-gray-900 leading-tight;
  }

  .user-role {
    @apply text-xs text-gray-500;
  }
}

// User dropdown
.user-dropdown {
  @apply absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 py-1 z-50;

  [dir="rtl"] & {
    @apply right-auto left-0;
  }

  &.hidden {
    display: none;
  }
}

.dropdown-item {
  @apply flex items-center space-x-3 px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 transition-colors duration-150;

  [dir="rtl"] & {
    @apply space-x-reverse;
  }

  svg {
    @apply flex-shrink-0;
  }

  // &.text-red-600 {
  //   @apply text-red-600 hover:bg-red-50;
  // }

}
&.error-text {
  color: #dc2626; // هذا هو اللون المطابق لـ text-red-600 في Tailwind
@apply text-red-600 hover:bg-red-50;
}

.dropdown-divider {
  @apply border-t border-gray-200 my-1;
}

// Responsive adjustments
@media (max-width: 767px) {
  .navbar {
    @apply text-sm;
  }

  .user-info {
    @apply hidden;
  }

  .breadcrumbs {
    @apply hidden;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .navbar {
    @apply text-sm;
  }
}

// Animation classes
.dropdown-enter {
  @apply opacity-0 scale-95;
}

.dropdown-enter-active {
  @apply transition-all duration-200 ease-out;
}

.dropdown-enter-to {
  @apply opacity-100 scale-100;
}

.dropdown-leave {
  @apply opacity-100 scale-100;
}

.dropdown-leave-active {
  @apply transition-all duration-150 ease-in;
}

.dropdown-leave-to {
  @apply opacity-0 scale-95;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .menu-toggle-btn,
  .notification-btn,
  .user-menu-btn,
  .dropdown-item {
    @apply transition-none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .navbar {
    @apply border-2 border-gray-900;
  }

  .menu-toggle-btn,
  .notification-btn,
  .user-menu-btn {
    @apply border border-gray-300;

    &:hover {
      @apply border-gray-900;
    }
  }

  .user-dropdown {
    @apply border-2 border-gray-900;
  }
}