import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LanguageService } from '../../core/services/language';
import { AuthService } from '../../core/services/auth';
import { LanguageSwitcherComponent } from '../../shared/components/language-switcher/language-switcher';

@Component({
  selector: 'app-navbar',
  standalone: true,
  imports: [CommonModule, LanguageSwitcherComponent],
  templateUrl: './navbar.html',
  styleUrl: './navbar.scss'
})
export class NavbarComponent implements OnInit, OnDestroy {
  @Input() isSidebarOpen = false;
  @Input() isMobile = false;
  @Input() isTablet = false;
  @Input() isDesktop = false;
  @Output() toggleSidebar = new EventEmitter<void>();

  currentUser: any = null;

  private destroy$ = new Subject<void>();

  constructor(
    private languageService: LanguageService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Subscribe to auth state changes
    this.authService.authState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(authState => {
        this.currentUser = authState.user;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  onToggleSidebar(): void {
    this.toggleSidebar.emit();
  }

  logout(): void {
    this.authService.logout();
  }

  get isRTL(): boolean {
    return this.languageService.isRTL();
  }

  get currentLanguage() {
    return this.languageService.getCurrentLanguage();
  }
}
