using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Reflection;
using Microsoft.AspNetCore.Authorization;

namespace ArmoredDoors.API.Filters;

public class PermissionOperationFilter : IOperationFilter
{
    public void Apply(OpenApiOperation operation, OperationFilterContext context)
    {
        // Check if the action has Authorize attribute
        var hasAuthorize = context.MethodInfo.DeclaringType?.GetCustomAttributes(true)
            .Union(context.MethodInfo.GetCustomAttributes(true))
            .OfType<AuthorizeAttribute>()
            .Any() ?? false;

        if (hasAuthorize)
        {
            operation.Responses.TryAdd("401", new OpenApiResponse { Description = "Unauthorized - Invalid or missing JWT token" });
            operation.Responses.TryAdd("403", new OpenApiResponse { Description = "Forbidden - Insufficient permissions" });

            // Add security requirement
            operation.Security = new List<OpenApiSecurityRequirement>
            {
                new OpenApiSecurityRequirement
                {
                    {
                        new OpenApiSecurityScheme
                        {
                            Reference = new OpenApiReference
                            {
                                Type = ReferenceType.SecurityScheme,
                                Id = "Bearer"
                            }
                        },
                        Array.Empty<string>()
                    }
                }
            };

            // Try to extract permission requirements from the method
            var permissionInfo = ExtractPermissionInfo(context.MethodInfo);
            if (!string.IsNullOrEmpty(permissionInfo))
            {
                if (operation.Description == null)
                    operation.Description = "";
                
                operation.Description += $"\n\n**Required Permission:** `{permissionInfo}`";
            }
        }

        // Add common response codes
        if (!operation.Responses.ContainsKey("400"))
            operation.Responses.TryAdd("400", new OpenApiResponse { Description = "Bad Request - Invalid input data" });
        
        if (!operation.Responses.ContainsKey("500"))
            operation.Responses.TryAdd("500", new OpenApiResponse { Description = "Internal Server Error" });
    }

    private static string ExtractPermissionInfo(MethodInfo methodInfo)
    {
        // This is a simplified version - in a real implementation, you might want to
        // analyze the method body or use custom attributes to specify required permissions
        var methodName = methodInfo.Name.ToLower();
        var controllerName = methodInfo.DeclaringType?.Name.Replace("Controller", "").ToLower();

        return controllerName switch
        {
            "users" => methodName switch
            {
                "getusers" or "getuser" => "Users.View",
                "createuser" => "Users.Create",
                "updateuser" => "Users.Edit",
                "deleteuser" => "Users.Delete",
                _ => "Users.View"
            },
            "partners" => methodName switch
            {
                "getpartners" or "getpartner" => "Partners.View",
                "createpartner" => "Partners.Create",
                "updatepartner" => "Partners.Edit",
                "deletepartner" => "Partners.Delete",
                _ => "Partners.View"
            },
            "rawmaterials" => methodName switch
            {
                "getrawmaterials" or "getrawmaterial" => "RawMaterials.View",
                "createrawmaterial" => "RawMaterials.Create",
                "updaterawmaterial" => "RawMaterials.Edit",
                "deleterawmaterial" => "RawMaterials.Delete",
                _ => "RawMaterials.View"
            },
            _ => ""
        };
    }
}
