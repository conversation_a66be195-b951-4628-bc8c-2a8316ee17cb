# Armored Doors Manufacturing Management System

A comprehensive web-based management system for armored doors manufacturing companies with multi-partner capital management, warehouse operations, and financial tracking.

## System Overview

This enterprise-level application provides complete business management capabilities including:

- **Multi-Partner Capital Management**: Real-time profit sharing based on capital contributions
- **Warehouse Management**: Raw materials and finished products with full inventory tracking
- **Financial Management**: Complete accounting, customer/supplier statements, and cash flow
- **Real-time Notifications**: SignalR-based notifications with permission-based filtering
- **Comprehensive Reporting**: Business intelligence with print and export capabilities
- **Multi-language Support**: Arabic and English with RTL support

## Technical Architecture

### Backend Stack
- **ASP.NET Core 8.0 Web API**
- **SQL Server** with Entity Framework Core
- **Unit of Work Pattern** for data access
- **AutoMapper** for object mapping
- **Redis** for caching and session management
- **SignalR** for real-time communications
- **JWT Authentication** with role-based authorization

### Frontend Stack
- **Angular 17+** with TypeScript
- **Tailwind CSS** for responsive design
- **Angular Material** for UI components
- **NgRx** for state management
- **Angular i18n** for localization

### Development Tools
- **Docker** for containerization
- **Entity Framework Migrations** for database versioning
- **Swagger/OpenAPI** for API documentation
- **xUnit** for testing

## Project Structure

```
ArmoredDoorsManagement/
├── src/
│   ├── Backend/
│   │   ├── ArmoredDoors.API/              # Web API project
│   │   ├── ArmoredDoors.Core/             # Domain models and interfaces
│   │   ├── ArmoredDoors.Infrastructure/   # Data access and external services
│   │   ├── ArmoredDoors.Application/      # Business logic and services
│   │   └── ArmoredDoors.Shared/           # Shared DTOs and utilities
│   ├── Frontend/
│   │   └── armored-doors-app/             # Angular application
│   ├── Database/
│   │   ├── Scripts/                       # SQL scripts and migrations
│   │   └── SeedData/                      # Initial data scripts
│   └── Tests/
│       ├── ArmoredDoors.UnitTests/        # Unit tests
│       ├── ArmoredDoors.IntegrationTests/ # Integration tests
│       └── ArmoredDoors.E2ETests/         # End-to-end tests
├── docs/                                  # Documentation
├── docker-compose.yml                     # Docker configuration
└── ArmoredDoorsManagement.sln            # Solution file
```

## Core Business Modules

### 1. Partner & Capital Management
- Capital contribution tracking
- Profit distribution calculations
- Partner account statements
- Capital injection/withdrawal workflows

### 2. Warehouse Management
- **Raw Materials**: Multi-unit inventory with location tracking
- **Finished Products**: Barcode generation and sales tracking
- **Inventory Movements**: Complete audit trail for all transactions

### 3. Financial Management
- Customer and supplier account management
- Aging reports and credit management
- Cash flow and treasury operations
- Employee advances and payroll

### 4. User Management & Security
- Role-based access control (RBAC)
- Granular permission system
- Multi-factor authentication support
- Audit logging for security events

## Getting Started

### Prerequisites
- .NET 8.0 SDK
- Node.js 18+ and npm
- SQL Server 2019+
- Redis Server
- Docker (optional)

### Development Setup
1. Clone the repository
2. Set up the database connection strings
3. Run Entity Framework migrations
4. Install frontend dependencies
5. Start the development servers

### Environment Configuration
- Development: Local SQL Server and Redis
- Staging: Docker containers with external databases
- Production: Kubernetes deployment with managed services

## Security Considerations

- JWT tokens with refresh token rotation
- API rate limiting and throttling
- SQL injection prevention through parameterized queries
- XSS protection with content security policies
- HTTPS enforcement in production
- Sensitive data encryption at rest

## Performance Optimization

- Redis caching for frequently accessed data
- Database indexing strategy
- Lazy loading for large datasets
- CDN integration for static assets
- Response compression and minification

## Compliance & Auditing

- Complete audit trail for all business transactions
- Financial data retention policies
- User activity logging
- Data backup and recovery procedures
- GDPR compliance for personal data handling

## Support & Maintenance

- Automated database backups
- Application monitoring and alerting
- Performance metrics collection
- Error logging and tracking
- Regular security updates

---

**Version**: 1.0.0  
**Last Updated**: 2025-06-26  
**License**: Proprietary
