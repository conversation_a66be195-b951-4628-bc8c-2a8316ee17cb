{"format": 1, "restore": {"D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.API\\ArmoredDoors.API.csproj": {}}, "projects": {"D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.API\\ArmoredDoors.API.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.API\\ArmoredDoors.API.csproj", "projectName": "ArmoredDoors.API", "projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.API\\ArmoredDoors.API.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.API\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Application\\ArmoredDoors.Application.csproj": {"projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Application\\ArmoredDoors.Application.csproj"}, "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Infrastructure\\ArmoredDoors.Infrastructure.csproj": {"projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Infrastructure\\ArmoredDoors.Infrastructure.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.AspNetCore.OpenApi": {"target": "Package", "version": "[9.0.3, )"}, "Microsoft.EntityFrameworkCore.Tools": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Application\\ArmoredDoors.Application.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Application\\ArmoredDoors.Application.csproj", "projectName": "ArmoredDoors.Application", "projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Application\\ArmoredDoors.Application.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Application\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj": {"projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj", "projectName": "ArmoredDoors.Core", "projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}, "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Infrastructure\\ArmoredDoors.Infrastructure.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Infrastructure\\ArmoredDoors.Infrastructure.csproj", "projectName": "ArmoredDoors.Infrastructure", "projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Infrastructure\\ArmoredDoors.Infrastructure.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Infrastructure\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files\\DevExpress 23.2\\Components\\Offline Packages", "C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\DevExpress 23.2.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net9.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\DevExpress 23.2\\Components\\System\\Components\\Packages": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "projectReferences": {"D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj": {"projectPath": "D:\\AIProjectTest\\Sourcs\\FullStackApp\\src\\Backend\\ArmoredDoors.Core\\ArmoredDoors.Core.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.200"}, "frameworks": {"net9.0": {"targetAlias": "net9.0", "dependencies": {"Microsoft.EntityFrameworkCore.Design": {"include": "Runtime, Build, Native, ContentFiles, Analyzers, BuildTransitive", "suppressParent": "All", "target": "Package", "version": "[9.0.6, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[9.0.6, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.201/PortableRuntimeIdentifierGraph.json"}}}}}