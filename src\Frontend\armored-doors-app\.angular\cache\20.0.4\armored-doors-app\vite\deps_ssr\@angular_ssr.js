import { createRequire } from 'module';const require = createRequire(import.meta.url);
import {
  AngularAppEngine,
  InlineCriticalCssProcessor,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  destroyAngularServerApp,
  extractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig,
  provideServerRendering,
  setAngularAppEngineManifest,
  setAngularAppManifest,
  withAppShell,
  withRoutes
} from "./chunk-EGLVM6FM.js";
import "./chunk-RJHWRNYY.js";
import "./chunk-5KWQXHYG.js";
import "./chunk-GY5VOL3W.js";
import "./chunk-VLE3A7F4.js";
import "./chunk-QX6UMTJX.js";
import "./chunk-4YZHNSA6.js";
import "./chunk-6BU3RPX3.js";
import "./chunk-43KPLV43.js";
import "./chunk-2XLRDDJW.js";
import "./chunk-TXGYY7YM.js";
import "./chunk-6DU2HRTW.js";
export {
  AngularAppEngine,
  PrerenderFallback,
  RenderMode,
  createRequestHandler,
  provideServerRendering,
  withAppShell,
  withRoutes,
  InlineCriticalCssProcessor as ɵInlineCriticalCssProcessor,
  destroyAngularServerApp as ɵdestroyAngularServerApp,
  extractRoutesAndCreateRouteTree as ɵextractRoutesAndCreateRouteTree,
  getOrCreateAngularServerApp as ɵgetOrCreateAngularServerApp,
  getRoutesFromAngularRouterConfig as ɵgetRoutesFromAngularRouterConfig,
  setAngularAppEngineManifest as ɵsetAngularAppEngineManifest,
  setAngularAppManifest as ɵsetAngularAppManifest
};
