version: '3.8'

services:
  # SQL Server Database
  sqlserver:
    image: mcr.microsoft.com/mssql/server:2022-latest
    container_name: armored-doors-sqlserver
    environment:
      - ACCEPT_EULA=Y
      - SA_PASSWORD=****************
      - MSSQL_PID=Developer
    ports:
      - "1433:1433"
    volumes:
      - sqlserver_data:/var/opt/mssql
    networks:
      - armored-doors-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: armored-doors-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - armored-doors-network
    command: redis-server --appendonly yes

  # Backend API
  api:
    build:
      context: .
      dockerfile: src/Backend/ArmoredDoors.API/Dockerfile
    container_name: armored-doors-api
    environment:
      - ASPNETCORE_ENVIRONMENT=Development
      - ASPNETCORE_URLS=http://+:80
      - ConnectionStrings__DefaultConnection=Server=sqlserver;Database=ArmoredDoorsDB;User Id=sa;Password=****************;TrustServerCertificate=true;
      - ConnectionStrings__Redis=redis:6379
      - JWT__SecretKey=ArmoredDoorsSecretKeyForJWTTokenGeneration2024!
      - JWT__Issuer=ArmoredDoorsAPI
      - JWT__Audience=ArmoredDoorsApp
    ports:
      - "5000:80"
    depends_on:
      - sqlserver
      - redis
    networks:
      - armored-doors-network
    volumes:
      - ./src/Backend/ArmoredDoors.API/wwwroot:/app/wwwroot

  # Frontend Angular App
  frontend:
    build:
      context: .
      dockerfile: src/Frontend/armored-doors-app/Dockerfile
    container_name: armored-doors-frontend
    ports:
      - "4200:80"
    depends_on:
      - api
    networks:
      - armored-doors-network
    environment:
      - API_URL=http://api:80

volumes:
  sqlserver_data:
    driver: local
  redis_data:
    driver: local

networks:
  armored-doors-network:
    driver: bridge
