namespace ArmoredDoors.API.Resources;

/// <summary>
/// Shared resource class for localization
/// This class is used as a key for accessing localized resources
/// </summary>
public class SharedResource
{
    // This class is intentionally empty.
    // It serves as a key for the IStringLocalizer<SharedResource> service
    // to access the corresponding .resx files (SharedResource.en.resx, SharedResource.ar.resx)
}
