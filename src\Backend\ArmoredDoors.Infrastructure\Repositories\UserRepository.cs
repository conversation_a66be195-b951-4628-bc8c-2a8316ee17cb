using Microsoft.EntityFrameworkCore;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Interfaces;
using ArmoredDoors.Infrastructure.Data;

namespace ArmoredDoors.Infrastructure.Repositories;

public class UserRepository : Repository<User>, IUserRepository
{
    public UserRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
                    .ThenInclude(r => r.RolePermissions)
                        .ThenInclude(rp => rp.Permission)
            .FirstOrDefaultAsync(u => u.Username == username, cancellationToken);
    }

    public async Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .FirstOrDefaultAsync(u => u.Email == email, cancellationToken);
    }

    public async Task<bool> IsUsernameUniqueAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(u => u.Username == username);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task<bool> IsEmailUniqueAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(u => u.Email == email);
        
        if (excludeUserId.HasValue)
        {
            query = query.Where(u => u.Id != excludeUserId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(u => u.UserRoles)
                .ThenInclude(ur => ur.Role)
            .Where(u => u.UserRoles.Any(ur => ur.Role.Name == roleName))
            .ToListAsync(cancellationToken);
    }
}

public class PartnerRepository : Repository<Partner>, IPartnerRepository
{
    public PartnerRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<Partner>> GetActivePartnersAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.IsActive)
            .OrderBy(p => p.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<decimal> GetTotalCapitalAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(p => p.IsActive)
            .SumAsync(p => p.CurrentCapitalAmount, cancellationToken);
    }

    public async Task<IEnumerable<Partner>> GetPartnersWithCapitalTransactionsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet
            .Include(p => p.CapitalTransactions)
            .Where(p => p.IsActive);

        if (fromDate.HasValue || toDate.HasValue)
        {
            query = query.Where(p => p.CapitalTransactions.Any(ct => 
                (!fromDate.HasValue || ct.TransactionDate >= fromDate.Value) &&
                (!toDate.HasValue || ct.TransactionDate <= toDate.Value)));
        }

        return await query.ToListAsync(cancellationToken);
    }

    public async Task UpdateOwnershipPercentagesAsync(CancellationToken cancellationToken = default)
    {
        var activePartners = await GetActivePartnersAsync(cancellationToken);
        var totalCapital = await GetTotalCapitalAsync(cancellationToken);

        if (totalCapital > 0)
        {
            foreach (var partner in activePartners)
            {
                partner.CurrentOwnershipPercentage = partner.CurrentCapitalAmount / totalCapital;
                partner.ModifiedDate = DateTime.UtcNow;
            }

            _dbSet.UpdateRange(activePartners);
        }
    }
}

public class CapitalTransactionRepository : Repository<CapitalTransaction>, ICapitalTransactionRepository
{
    public CapitalTransactionRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<IEnumerable<CapitalTransaction>> GetByPartnerIdAsync(Guid partnerId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(ct => ct.Partner)
            .Include(ct => ct.ApprovedByUser)
            .Include(ct => ct.CreatedByUser)
            .Where(ct => ct.PartnerId == partnerId)
            .OrderByDescending(ct => ct.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<CapitalTransaction>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(ct => ct.Partner)
            .Include(ct => ct.ApprovedByUser)
            .Include(ct => ct.CreatedByUser)
            .Where(ct => ct.TransactionDate >= fromDate && ct.TransactionDate <= toDate)
            .OrderByDescending(ct => ct.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<decimal> GetTotalInjectionsAsync(Guid? partnerId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(ct => ct.TransactionType == "Injection");

        if (partnerId.HasValue)
            query = query.Where(ct => ct.PartnerId == partnerId.Value);

        if (fromDate.HasValue)
            query = query.Where(ct => ct.TransactionDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ct => ct.TransactionDate <= toDate.Value);

        return await query.SumAsync(ct => ct.Amount, cancellationToken);
    }

    public async Task<decimal> GetTotalWithdrawalsAsync(Guid? partnerId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(ct => ct.TransactionType == "Withdrawal");

        if (partnerId.HasValue)
            query = query.Where(ct => ct.PartnerId == partnerId.Value);

        if (fromDate.HasValue)
            query = query.Where(ct => ct.TransactionDate >= fromDate.Value);

        if (toDate.HasValue)
            query = query.Where(ct => ct.TransactionDate <= toDate.Value);

        return await query.SumAsync(ct => ct.Amount, cancellationToken);
    }
}

public class RawMaterialRepository : Repository<RawMaterial>, IRawMaterialRepository
{
    public RawMaterialRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<RawMaterial?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(rm => rm.Category)
            .Include(rm => rm.MeasurementUnit)
            .Include(rm => rm.DefaultStorageLocation)
            .Include(rm => rm.Images)
            .FirstOrDefaultAsync(rm => rm.Code == code, cancellationToken);
    }

    public async Task<IEnumerable<RawMaterial>> GetByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(rm => rm.Category)
            .Include(rm => rm.MeasurementUnit)
            .Where(rm => rm.CategoryId == categoryId && rm.IsActive)
            .OrderBy(rm => rm.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<RawMaterial>> GetLowStockItemsAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(rm => rm.Category)
            .Include(rm => rm.MeasurementUnit)
            .Where(rm => rm.IsActive && rm.CurrentStockQuantity <= rm.MinimumStockLevel)
            .OrderBy(rm => rm.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(rm => rm.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(rm => rm.Id != excludeId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task UpdateStockQuantityAsync(Guid id, decimal quantity, CancellationToken cancellationToken = default)
    {
        var rawMaterial = await GetByIdAsync(id, cancellationToken);
        if (rawMaterial != null)
        {
            rawMaterial.CurrentStockQuantity = quantity;
            rawMaterial.ModifiedDate = DateTime.UtcNow;
            _dbSet.Update(rawMaterial);
        }
    }
}
