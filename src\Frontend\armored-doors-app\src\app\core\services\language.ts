import { Injectable, Inject, LOCALE_ID } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DOCUMENT } from '@angular/common';
import { environment } from '../../../environments/environment';

export interface Language {
  code: string;
  name: string;
  nativeName: string;
  direction: 'ltr' | 'rtl';
  flag: string;
}

@Injectable({
  providedIn: 'root'
})
export class LanguageService {
  private readonly LANGUAGE_KEY = 'selected_language';

  private readonly supportedLanguages: Language[] = [
    {
      code: 'en',
      name: 'English',
      nativeName: 'English',
      direction: 'ltr',
      flag: '🇺🇸'
    },
    {
      code: 'ar',
      name: 'Arabic',
      nativeName: 'العربية',
      direction: 'rtl',
      flag: '🇪🇬'
    }
  ];

  private currentLanguageSubject = new BehaviorSubject<Language>(
    this.getLanguageByCode(environment.defaultLanguage)
  );

  public currentLanguage$ = this.currentLanguageSubject.asObservable();

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(LOCALE_ID) private localeId: string
  ) {
    this.initializeLanguage();
  }

  private initializeLanguage(): void {
    const savedLanguage = localStorage.getItem(this.LANGUAGE_KEY);
    const browserLanguage = this.getBrowserLanguage();

    let languageCode = savedLanguage || browserLanguage || environment.defaultLanguage;

    // Ensure the language is supported
    if (!this.isLanguageSupported(languageCode)) {
      languageCode = environment.defaultLanguage;
    }

    this.setLanguage(languageCode);
  }

  private getBrowserLanguage(): string {
    const browserLang = navigator.language || (navigator as any).userLanguage;
    return browserLang.split('-')[0]; // Get language code without region
  }

  private isLanguageSupported(code: string): boolean {
    return this.supportedLanguages.some(lang => lang.code === code);
  }

  private getLanguageByCode(code: string): Language {
    return this.supportedLanguages.find(lang => lang.code === code) || this.supportedLanguages[0];
  }

  setLanguage(code: string): void {
    if (!this.isLanguageSupported(code)) {
      console.warn(`Language ${code} is not supported. Falling back to ${environment.defaultLanguage}`);
      code = environment.defaultLanguage;
    }

    const language = this.getLanguageByCode(code);

    // Save to localStorage
    localStorage.setItem(this.LANGUAGE_KEY, code);

    // Update document direction and language
    this.document.documentElement.dir = language.direction;
    this.document.documentElement.lang = code;

    // Update body class for styling
    this.document.body.classList.remove('rtl', 'ltr');
    this.document.body.classList.add(language.direction);

    // Update current language
    this.currentLanguageSubject.next(language);
  }

  getCurrentLanguage(): Language {
    return this.currentLanguageSubject.value;
  }

  getCurrentLanguageCode(): string {
    return this.currentLanguageSubject.value.code;
  }

  getSupportedLanguages(): Language[] {
    return [...this.supportedLanguages];
  }

  isRTL(): boolean {
    return this.currentLanguageSubject.value.direction === 'rtl';
  }

  isLTR(): boolean {
    return this.currentLanguageSubject.value.direction === 'ltr';
  }

  toggleLanguage(): void {
    const currentCode = this.getCurrentLanguageCode();
    const newCode = currentCode === 'en' ? 'ar' : 'en';
    this.setLanguage(newCode);
  }

  // Get localized text direction class
  getDirectionClass(): string {
    return this.isRTL() ? 'rtl' : 'ltr';
  }

  // Get text alignment class based on direction
  getTextAlignClass(): string {
    return this.isRTL() ? 'text-right' : 'text-left';
  }

  // Get margin/padding classes for RTL support
  getMarginStartClass(size: string): string {
    return this.isRTL() ? `mr-${size}` : `ml-${size}`;
  }

  getMarginEndClass(size: string): string {
    return this.isRTL() ? `ml-${size}` : `mr-${size}`;
  }

  getPaddingStartClass(size: string): string {
    return this.isRTL() ? `pr-${size}` : `pl-${size}`;
  }

  getPaddingEndClass(size: string): string {
    return this.isRTL() ? `pl-${size}` : `pr-${size}`;
  }
}
