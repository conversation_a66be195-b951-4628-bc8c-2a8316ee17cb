<div class="relative inline-block text-left">
  <!-- Quick Toggle Button (for mobile/compact view) -->
  <button
    type="button"
    class="theme-toggle-btn quick-toggle md:hidden"
    (click)="quickToggle()"
    [attr.aria-label]="isRTL ? 'تبديل المظهر' : 'Toggle theme'"
    [title]="isRTL ? 'تبديل المظهر' : 'Toggle theme'">
    <svg class="w-5 h-5 transition-transform duration-200"
         [class.rotate-180]="currentTheme.isDark"
         fill="none" stroke="currentColor" viewBox="0 0 24 24">
      <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
            [attr.d]="getThemeIcon(currentTheme.isDark ? 'dark' : 'light')"></path>
    </svg>
  </button>

  <!-- Full Theme Selector (for desktop) -->
  <div class="hidden md:block">
    <button
      type="button"
      class="theme-toggle-btn dropdown-toggle"
      (click)="toggleDropdown()"
      (keydown)="onKeyDown($event)"
      [attr.aria-expanded]="isDropdownOpen"
      [attr.aria-label]="isRTL ? 'اختيار المظهر' : 'Select theme'"
      aria-haspopup="true">
      <div class="flex items-center space-x-2">
        <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                [attr.d]="getThemeIcon(currentTheme.mode)"></path>
        </svg>
        <span class="hidden lg:block text-sm font-medium">
          {{ getThemeLabel(currentTheme.mode) }}
        </span>
        <svg class="w-4 h-4 transition-transform duration-200"
             [class.rotate-180]="isDropdownOpen"
             fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                d="M19 9l-7 7-7-7"></path>
        </svg>
      </div>
    </button>

    <!-- Dropdown Menu -->
    <div
      *ngIf="isDropdownOpen"
      class="theme-dropdown"
      [class.dropdown-rtl]="isRTL">
      <div class="py-1" role="menu" aria-orientation="vertical">
        <button
          *ngFor="let mode of ['light', 'dark', 'auto']"
          type="button"
          class="theme-option"
          [class.active]="currentTheme.mode === mode"
          (click)="setTheme(mode)"
          role="menuitem">
          <div class="flex items-center space-x-3">
            <svg class="w-5 h-5 flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    [attr.d]="getThemeIcon(mode)"></path>
            </svg>
            <span class="flex-1 text-left">{{ getThemeLabel(mode) }}</span>
            <svg
              *ngIf="currentTheme.mode === mode"
              class="w-4 h-4 text-primary-600 dark:text-primary-400"
              fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                    d="M5 13l4 4L19 7"></path>
            </svg>
          </div>
        </button>
      </div>
    </div>
  </div>

  <!-- Backdrop to close dropdown when clicking outside -->
  <div
    *ngIf="isDropdownOpen"
    class="fixed inset-0 z-40"
    (click)="closeDropdown()"
    aria-hidden="true">
  </div>
</div>
