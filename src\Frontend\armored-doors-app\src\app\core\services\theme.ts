import { Injectable, Inject, PLATFORM_ID } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { DOCUMENT, isPlatformBrowser } from '@angular/common';

export type ThemeMode = 'light' | 'dark' | 'auto';

export interface ThemeState {
  mode: ThemeMode;
  isDark: boolean;
  isAuto: boolean;
}

@Injectable({
  providedIn: 'root'
})
export class ThemeService {
  private readonly THEME_KEY = 'theme_preference';
  private readonly DARK_CLASS = 'dark';

  private themeStateSubject = new BehaviorSubject<ThemeState>({
    mode: 'light',
    isDark: false,
    isAuto: false
  });

  public themeState$ = this.themeStateSubject.asObservable();

  constructor(
    @Inject(DOCUMENT) private document: Document,
    @Inject(PLATFORM_ID) private platformId: Object
  ) {
    this.initializeTheme();
  }

  private initializeTheme(): void {
    let savedTheme: ThemeMode = 'light';

    // Only access localStorage in browser environment
    if (isPlatformBrowser(this.platformId)) {
      const stored = localStorage.getItem(this.THEME_KEY) as ThemeMode;
      savedTheme = stored || this.getSystemPreference();
    }

    this.setTheme(savedTheme);

    // Listen for system theme changes
    if (isPlatformBrowser(this.platformId)) {
      this.watchSystemTheme();
    }
  }

  private getSystemPreference(): ThemeMode {
    if (isPlatformBrowser(this.platformId) && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light';
    }
    return 'light';
  }

  private watchSystemTheme(): void {
    if (window.matchMedia) {
      const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
      mediaQuery.addEventListener('change', (e) => {
        const currentState = this.themeStateSubject.value;
        if (currentState.mode === 'auto') {
          this.applyTheme(e.matches ? 'dark' : 'light');
        }
      });
    }
  }

  setTheme(mode: ThemeMode): void {
    // Save to localStorage only in browser environment
    if (isPlatformBrowser(this.platformId)) {
      localStorage.setItem(this.THEME_KEY, mode);
    }

    let actualTheme: 'light' | 'dark';

    if (mode === 'auto') {
      actualTheme = this.getSystemPreference() === 'dark' ? 'dark' : 'light';
    } else {
      actualTheme = mode;
    }

    this.applyTheme(actualTheme);

    // Update state
    this.themeStateSubject.next({
      mode,
      isDark: actualTheme === 'dark',
      isAuto: mode === 'auto'
    });
  }

  private applyTheme(theme: 'light' | 'dark'): void {
    const htmlElement = this.document.documentElement;

    if (theme === 'dark') {
      htmlElement.classList.add(this.DARK_CLASS);
    } else {
      htmlElement.classList.remove(this.DARK_CLASS);
    }

    // Update meta theme-color for mobile browsers
    this.updateMetaThemeColor(theme);
  }

  private updateMetaThemeColor(theme: 'light' | 'dark'): void {
    if (isPlatformBrowser(this.platformId)) {
      let metaThemeColor = this.document.querySelector('meta[name="theme-color"]');

      if (!metaThemeColor) {
        metaThemeColor = this.document.createElement('meta');
        metaThemeColor.setAttribute('name', 'theme-color');
        this.document.head.appendChild(metaThemeColor);
      }

      const color = theme === 'dark' ? '#1f2937' : '#ffffff';
      metaThemeColor.setAttribute('content', color);
    }
  }

  toggleTheme(): void {
    const currentState = this.themeStateSubject.value;
    const newMode: ThemeMode = currentState.isDark ? 'light' : 'dark';
    this.setTheme(newMode);
  }

  // Getters
  getCurrentTheme(): ThemeState {
    return this.themeStateSubject.value;
  }

  isDarkMode(): boolean {
    return this.themeStateSubject.value.isDark;
  }

  isLightMode(): boolean {
    return !this.themeStateSubject.value.isDark;
  }

  getThemeMode(): ThemeMode {
    return this.themeStateSubject.value.mode;
  }
}
