// Sidebar Styles with dark mode support
.sidebar {
  @apply h-full flex flex-col shadow-lg transition-colors duration-300;

  // Light mode
  @apply bg-white border-r border-gray-200;

  // Dark mode
  @apply dark:bg-gray-800 dark:border-gray-700;

  // RTL Support
  [dir="rtl"] & {
    @apply border-r-0 border-l border-gray-200 dark:border-l-gray-700;
  }

  // Responsive width
  &.mobile, &.tablet {
    @apply w-64;
  }

  &.desktop {
    @apply w-64 transition-all duration-300;

    &.collapsed {
      @apply w-16;
    }
  }
}

// Sidebar Header
.sidebar-header {
  @apply flex items-center justify-between p-4 transition-colors duration-300;

  // Light mode
  @apply border-b border-gray-200 bg-gray-50;

  // Dark mode
  @apply dark:border-b-gray-700 dark:bg-gray-900/50;

  .logo-container {
    @apply flex items-center space-x-3 transition-all duration-300;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }

    &.collapsed {
      @apply justify-center;

      .logo-text {
        display: none;
      }
    }
  }

  .logo-icon {
    @apply flex-shrink-0;
  }

  .logo-text {
    @apply transition-opacity duration-300;

    h1 {
      @apply text-lg font-bold leading-tight transition-colors duration-300;

      // Light mode
      @apply text-gray-900;

      // Dark mode
      @apply dark:text-white;

      // Arabic text enhancement
      [dir="rtl"] & {
        @apply font-black;
      }
    }

    p {
      @apply text-xs mt-1 transition-colors duration-300;

      // Light mode
      @apply text-gray-500;

      // Dark mode
      @apply dark:text-gray-400;

      // Arabic text enhancement
      [dir="rtl"] & {
        @apply font-medium;
      }
    }
  }

  .close-btn {
    @apply p-2 rounded-lg transition-colors duration-200 lg:hidden;

    // Light mode
    @apply text-gray-500 hover:text-gray-700 hover:bg-gray-100;

    // Dark mode
    @apply dark:text-gray-400 dark:hover:text-gray-200 dark:hover:bg-gray-700;
  }
}

// Navigation
.sidebar-nav {
  @apply flex-1 overflow-y-auto py-4;

  // Custom scrollbar
  &::-webkit-scrollbar {
    @apply w-2;
  }

  &::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  &::-webkit-scrollbar-thumb {
    @apply bg-gray-300 rounded-full;

    &:hover {
      @apply bg-gray-400;
    }
  }
}

.nav-list {
  @apply space-y-1 px-3;
}

.nav-item {
  @apply w-full;
}

// Navigation Buttons
.nav-button {
  @apply w-full text-left rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;

  [dir="rtl"] & {
    @apply text-right;
  }

  &:hover {
    @apply bg-gray-100;
  }

  &.active {
    @apply bg-primary-100 text-primary-700 border border-primary-200;

    .nav-icon {
      @apply text-primary-600;
    }
  }

  // Group button (has children)
  &.group-button {
    @apply p-3;

    &.expanded {
      @apply bg-gray-50;
    }
  }

  // Single button (no children)
  &.single-button {
    @apply p-3;
  }

  // Submenu button
  &.submenu-button {
    @apply p-2 ml-4;

    [dir="rtl"] & {
      @apply ml-0 mr-4;
    }

    &:hover {
      @apply bg-gray-50;
    }

    &.active {
      @apply bg-primary-50 text-primary-600;
    }
  }
}

.nav-button-content {
  @apply flex items-center justify-between;
}

.nav-icon {
  @apply flex-shrink-0 text-gray-500 transition-colors duration-200;

  &.submenu-icon {
    @apply text-gray-400;
  }
}

.nav-label {
  @apply flex-1 text-sm font-medium text-gray-700 transition-all duration-300;

  [dir="rtl"] & {
    @apply text-right;
  }

  .nav-button.active & {
    @apply text-primary-700;
  }

  .submenu-button & {
    @apply text-gray-600 text-xs;
  }

  .submenu-button.active & {
    @apply text-primary-600;
  }
}

.nav-arrow {
  @apply flex-shrink-0 text-gray-400;

  [dir="rtl"] & {
    @apply transform rotate-180;
  }
}

// Submenu
.submenu {
  @apply overflow-hidden transition-all duration-300 ease-in-out;
  max-height: 0;

  &.expanded {
    max-height: 500px;
    @apply mt-2;
  }
}

.submenu-item {
  @apply w-full;
}

// Badges
.nav-badge {
  @apply px-2 py-1 text-xs font-medium rounded-full;

  &.badge-primary {
    @apply bg-primary-100 text-primary-700;
  }

  &.badge-success {
    @apply bg-green-100 text-green-700;
  }

  &.badge-warning {
    @apply bg-yellow-100 text-yellow-700;
  }

  &.badge-danger {
    @apply bg-red-100 text-red-700;
  }
}

// Sidebar Footer
.sidebar-footer {
  @apply border-t border-gray-200 p-4 bg-gray-50;

  .footer-content {
    @apply space-y-1;
  }
}

// Responsive Adjustments
@media (max-width: 767px) {
  .sidebar {
    @apply text-sm;
  }

  .nav-button {
    @apply p-2;
  }

  .nav-label {
    @apply text-sm;
  }

  .sidebar-header {
    @apply p-3;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  .sidebar {
    @apply text-sm;
  }
}

// Collapsed state adjustments
.sidebar.collapsed {
  .nav-button {
    @apply justify-center p-3;
  }

  .nav-button-content {
    @apply justify-center;
  }

  .nav-label,
  .nav-arrow,
  .nav-badge {
    display: none;
  }

  .submenu {
    display: none;
  }

  // Tooltip for collapsed items
  .nav-button {
    @apply relative;

    &:hover::after {
      content: attr(title);
      @apply absolute left-full ml-2 px-2 py-1 bg-gray-900 text-white text-xs rounded whitespace-nowrap z-50;

      [dir="rtl"] & {
        @apply left-auto right-full ml-0 mr-2;
      }
    }
  }
}

// Animation classes
.fade-enter {
  @apply opacity-0;
}

.fade-enter-active {
  @apply transition-opacity duration-300;
}

.fade-enter-to {
  @apply opacity-100;
}

.fade-leave {
  @apply opacity-100;
}

.fade-leave-active {
  @apply transition-opacity duration-300;
}

.fade-leave-to {
  @apply opacity-0;
}

// Accessibility improvements
@media (prefers-reduced-motion: reduce) {
  .sidebar,
  .nav-button,
  .submenu,
  .nav-icon,
  .nav-label,
  .nav-arrow {
    @apply transition-none;
  }
}

// High contrast mode
@media (prefers-contrast: high) {
  .sidebar {
    @apply border-2 border-gray-900;
  }

  .nav-button {
    @apply border border-gray-300;

    &:hover {
      @apply border-gray-900;
    }

    &.active {
      @apply border-2 border-primary-600;
    }
  }
}