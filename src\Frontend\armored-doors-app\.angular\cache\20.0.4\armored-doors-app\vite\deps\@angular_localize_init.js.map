{"version": 3, "sources": ["../../../../../../node_modules/@angular/localize/fesm2022/localize2.mjs", "../../../../../../node_modules/@angular/localize/fesm2022/init.mjs"], "sourcesContent": ["/**\n * @license Angular v20.0.5\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\n/**\n * The character used to mark the start and end of a \"block\" in a `$localize` tagged string.\n * A block can indicate metadata about the message or specify a name of a placeholder for a\n * substitution expressions.\n *\n * For example:\n *\n * ```ts\n * $localize`Hello, ${title}:title:!`;\n * $localize`:meaning|description@@id:source message text`;\n * ```\n */\nconst BLOCK_MARKER$1 = ':';\n/**\n * The marker used to separate a message's \"meaning\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:correct|Indicates that the user got the answer correct: Right!`;\n * $localize `:movement|Button label for moving to the right: Right!`;\n * ```\n */\nconst MEANING_SEPARATOR = '|';\n/**\n * The marker used to separate a message's custom \"id\" from its \"description\" in a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:A welcome message on the home page@@myApp-homepage-welcome: Welcome!`;\n * ```\n */\nconst ID_SEPARATOR = '@@';\n/**\n * The marker used to separate legacy message ids from the rest of a metadata block.\n *\n * For example:\n *\n * ```ts\n * $localize `:@@custom-id␟2df64767cd895a8fabe3e18b94b5b6b6f9e2e3f0: Welcome!`;\n * ```\n *\n * Note that this character is the \"symbol for the unit separator\" (␟) not the \"unit separator\n * character\" itself, since that has no visual representation. See https://graphemica.com/%E2%90%9F.\n *\n * Here is some background for the original \"unit separator character\":\n * https://stackoverflow.com/questions/8695118/whats-the-file-group-record-unit-separator-control-characters-and-its-usage\n */\nconst LEGACY_ID_INDICATOR = '\\u241F';\n\n/**\n * A lazily created TextEncoder instance for converting strings into UTF-8 bytes\n */\nlet textEncoder;\n/**\n * Compute the fingerprint of the given string\n *\n * The output is 64 bit number encoded as a decimal string\n *\n * based on:\n * https://github.com/google/closure-compiler/blob/master/src/com/google/javascript/jscomp/GoogleJsMessageIdGenerator.java\n */\nfunction fingerprint(str) {\n    textEncoder ??= new TextEncoder();\n    const utf8 = textEncoder.encode(str);\n    const view = new DataView(utf8.buffer, utf8.byteOffset, utf8.byteLength);\n    let hi = hash32(view, utf8.length, 0);\n    let lo = hash32(view, utf8.length, 102072);\n    if (hi == 0 && (lo == 0 || lo == 1)) {\n        hi = hi ^ 0x130f9bef;\n        lo = lo ^ -0x6b5f56d8;\n    }\n    return (BigInt.asUintN(32, BigInt(hi)) << BigInt(32)) | BigInt.asUintN(32, BigInt(lo));\n}\nfunction computeMsgId(msg, meaning = '') {\n    let msgFingerprint = fingerprint(msg);\n    if (meaning) {\n        // Rotate the 64-bit message fingerprint one bit to the left and then add the meaning\n        // fingerprint.\n        msgFingerprint =\n            BigInt.asUintN(64, msgFingerprint << BigInt(1)) |\n                ((msgFingerprint >> BigInt(63)) & BigInt(1));\n        msgFingerprint += fingerprint(meaning);\n    }\n    return BigInt.asUintN(63, msgFingerprint).toString();\n}\nfunction hash32(view, length, c) {\n    let a = 0x9e3779b9, b = 0x9e3779b9;\n    let index = 0;\n    const end = length - 12;\n    for (; index <= end; index += 12) {\n        a += view.getUint32(index, true);\n        b += view.getUint32(index + 4, true);\n        c += view.getUint32(index + 8, true);\n        const res = mix(a, b, c);\n        (a = res[0]), (b = res[1]), (c = res[2]);\n    }\n    const remainder = length - index;\n    // the first byte of c is reserved for the length\n    c += length;\n    if (remainder >= 4) {\n        a += view.getUint32(index, true);\n        index += 4;\n        if (remainder >= 8) {\n            b += view.getUint32(index, true);\n            index += 4;\n            // Partial 32-bit word for c\n            if (remainder >= 9) {\n                c += view.getUint8(index++) << 8;\n            }\n            if (remainder >= 10) {\n                c += view.getUint8(index++) << 16;\n            }\n            if (remainder === 11) {\n                c += view.getUint8(index++) << 24;\n            }\n        }\n        else {\n            // Partial 32-bit word for b\n            if (remainder >= 5) {\n                b += view.getUint8(index++);\n            }\n            if (remainder >= 6) {\n                b += view.getUint8(index++) << 8;\n            }\n            if (remainder === 7) {\n                b += view.getUint8(index++) << 16;\n            }\n        }\n    }\n    else {\n        // Partial 32-bit word for a\n        if (remainder >= 1) {\n            a += view.getUint8(index++);\n        }\n        if (remainder >= 2) {\n            a += view.getUint8(index++) << 8;\n        }\n        if (remainder === 3) {\n            a += view.getUint8(index++) << 16;\n        }\n    }\n    return mix(a, b, c)[2];\n}\nfunction mix(a, b, c) {\n    a -= b;\n    a -= c;\n    a ^= c >>> 13;\n    b -= c;\n    b -= a;\n    b ^= a << 8;\n    c -= a;\n    c -= b;\n    c ^= b >>> 13;\n    a -= b;\n    a -= c;\n    a ^= c >>> 12;\n    b -= c;\n    b -= a;\n    b ^= a << 16;\n    c -= a;\n    c -= b;\n    c ^= b >>> 5;\n    a -= b;\n    a -= c;\n    a ^= c >>> 3;\n    b -= c;\n    b -= a;\n    b ^= a << 10;\n    c -= a;\n    c -= b;\n    c ^= b >>> 15;\n    return [a, b, c];\n}\n// Utils\nvar Endian;\n(function (Endian) {\n    Endian[Endian[\"Little\"] = 0] = \"Little\";\n    Endian[Endian[\"Big\"] = 1] = \"Big\";\n})(Endian || (Endian = {}));\n\n// This module specifier is intentionally a relative path to allow bundling the code directly\n// into the package.\n// @ng_package: ignore-cross-repo-import\n/**\n * Parse a `$localize` tagged string into a structure that can be used for translation or\n * extraction.\n *\n * See `ParsedMessage` for an example.\n */\nfunction parseMessage(messageParts, expressions, location, messagePartLocations, expressionLocations = []) {\n    const substitutions = {};\n    const substitutionLocations = {};\n    const associatedMessageIds = {};\n    const metadata = parseMetadata(messageParts[0], messageParts.raw[0]);\n    const cleanedMessageParts = [metadata.text];\n    const placeholderNames = [];\n    let messageString = metadata.text;\n    for (let i = 1; i < messageParts.length; i++) {\n        const { messagePart, placeholderName = computePlaceholderName(i), associatedMessageId, } = parsePlaceholder(messageParts[i], messageParts.raw[i]);\n        messageString += `{$${placeholderName}}${messagePart}`;\n        if (expressions !== undefined) {\n            substitutions[placeholderName] = expressions[i - 1];\n            substitutionLocations[placeholderName] = expressionLocations[i - 1];\n        }\n        placeholderNames.push(placeholderName);\n        if (associatedMessageId !== undefined) {\n            associatedMessageIds[placeholderName] = associatedMessageId;\n        }\n        cleanedMessageParts.push(messagePart);\n    }\n    const messageId = metadata.customId || computeMsgId(messageString, metadata.meaning || '');\n    const legacyIds = metadata.legacyIds ? metadata.legacyIds.filter((id) => id !== messageId) : [];\n    return {\n        id: messageId,\n        legacyIds,\n        substitutions,\n        substitutionLocations,\n        text: messageString,\n        customId: metadata.customId,\n        meaning: metadata.meaning || '',\n        description: metadata.description || '',\n        messageParts: cleanedMessageParts,\n        messagePartLocations,\n        placeholderNames,\n        associatedMessageIds,\n        location,\n    };\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract the message metadata from the text.\n *\n * If the message part has a metadata block this function will extract the `meaning`,\n * `description`, `customId` and `legacyId` (if provided) from the block. These metadata properties\n * are serialized in the string delimited by `|`, `@@` and `␟` respectively.\n *\n * (Note that `␟` is the `LEGACY_ID_INDICATOR` - see `constants.ts`.)\n *\n * For example:\n *\n * ```ts\n * `:meaning|description@@custom-id:`\n * `:meaning|@@custom-id:`\n * `:meaning|description:`\n * `:description@@custom-id:`\n * `:meaning|:`\n * `:description:`\n * `:@@custom-id:`\n * `:meaning|description@@custom-id␟legacy-id-1␟legacy-id-2:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing any metadata that was parsed from the message part.\n */\nfunction parseMetadata(cooked, raw) {\n    const { text: messageString, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { text: messageString };\n    }\n    else {\n        const [meaningDescAndId, ...legacyIds] = block.split(LEGACY_ID_INDICATOR);\n        const [meaningAndDesc, customId] = meaningDescAndId.split(ID_SEPARATOR, 2);\n        let [meaning, description] = meaningAndDesc.split(MEANING_SEPARATOR, 2);\n        if (description === undefined) {\n            description = meaning;\n            meaning = undefined;\n        }\n        if (description === '') {\n            description = undefined;\n        }\n        return { text: messageString, meaning, description, customId, legacyIds };\n    }\n}\n/**\n * Parse the given message part (`cooked` + `raw`) to extract any placeholder metadata from the\n * text.\n *\n * If the message part has a metadata block this function will extract the `placeholderName` and\n * `associatedMessageId` (if provided) from the block.\n *\n * These metadata properties are serialized in the string delimited by `@@`.\n *\n * For example:\n *\n * ```ts\n * `:placeholder-name@@associated-id:`\n * ```\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns A object containing the metadata (`placeholderName` and `associatedMessageId`) of the\n *     preceding placeholder, along with the static text that follows.\n */\nfunction parsePlaceholder(cooked, raw) {\n    const { text: messagePart, block } = splitBlock(cooked, raw);\n    if (block === undefined) {\n        return { messagePart };\n    }\n    else {\n        const [placeholderName, associatedMessageId] = block.split(ID_SEPARATOR);\n        return { messagePart, placeholderName, associatedMessageId };\n    }\n}\n/**\n * Split a message part (`cooked` + `raw`) into an optional delimited \"block\" off the front and the\n * rest of the text of the message part.\n *\n * Blocks appear at the start of message parts. They are delimited by a colon `:` character at the\n * start and end of the block.\n *\n * If the block is in the first message part then it will be metadata about the whole message:\n * meaning, description, id.  Otherwise it will be metadata about the immediately preceding\n * substitution: placeholder name.\n *\n * Since blocks are optional, it is possible that the content of a message block actually starts\n * with a block marker. In this case the marker must be escaped `\\:`.\n *\n * @param cooked The cooked version of the message part to parse.\n * @param raw The raw version of the message part to parse.\n * @returns An object containing the `text` of the message part and the text of the `block`, if it\n * exists.\n * @throws an error if the `block` is unterminated\n */\nfunction splitBlock(cooked, raw) {\n    if (raw.charAt(0) !== BLOCK_MARKER$1) {\n        return { text: cooked };\n    }\n    else {\n        const endOfBlock = findEndOfBlock(cooked, raw);\n        return {\n            block: cooked.substring(1, endOfBlock),\n            text: cooked.substring(endOfBlock + 1),\n        };\n    }\n}\nfunction computePlaceholderName(index) {\n    return index === 1 ? 'PH' : `PH_${index - 1}`;\n}\n/**\n * Find the end of a \"marked block\" indicated by the first non-escaped colon.\n *\n * @param cooked The cooked string (where escaped chars have been processed)\n * @param raw The raw string (where escape sequences are still in place)\n *\n * @returns the index of the end of block marker\n * @throws an error if the block is unterminated\n */\nfunction findEndOfBlock(cooked, raw) {\n    for (let cookedIndex = 1, rawIndex = 1; cookedIndex < cooked.length; cookedIndex++, rawIndex++) {\n        if (raw[rawIndex] === '\\\\') {\n            rawIndex++;\n        }\n        else if (cooked[cookedIndex] === BLOCK_MARKER$1) {\n            return cookedIndex;\n        }\n    }\n    throw new Error(`Unterminated $localize metadata block in \"${raw}\".`);\n}\n\n/**\n * Tag a template literal string for localization.\n *\n * For example:\n *\n * ```ts\n * $localize `some string to localize`\n * ```\n *\n * **Providing meaning, description and id**\n *\n * You can optionally specify one or more of `meaning`, `description` and `id` for a localized\n * string by pre-pending it with a colon delimited block of the form:\n *\n * ```ts\n * $localize`:meaning|description@@id:source message text`;\n *\n * $localize`:meaning|:source message text`;\n * $localize`:description:source message text`;\n * $localize`:@@id:source message text`;\n * ```\n *\n * This format is the same as that used for `i18n` markers in Angular templates. See the\n * [Angular i18n guide](guide/i18n/prepare#mark-text-in-component-template).\n *\n * **Naming placeholders**\n *\n * If the template literal string contains expressions, then the expressions will be automatically\n * associated with placeholder names for you.\n *\n * For example:\n *\n * ```ts\n * $localize `Hi ${name}! There are ${items.length} items.`;\n * ```\n *\n * will generate a message-source of `Hi {$PH}! There are {$PH_1} items`.\n *\n * The recommended practice is to name the placeholder associated with each expression though.\n *\n * Do this by providing the placeholder name wrapped in `:` characters directly after the\n * expression. These placeholder names are stripped out of the rendered localized string.\n *\n * For example, to name the `items.length` expression placeholder `itemCount` you write:\n *\n * ```ts\n * $localize `There are ${items.length}:itemCount: items`;\n * ```\n *\n * **Escaping colon markers**\n *\n * If you need to use a `:` character directly at the start of a tagged string that has no\n * metadata block, or directly after a substitution expression that has no name you must escape\n * the `:` by preceding it with a backslash:\n *\n * For example:\n *\n * ```ts\n * // message has a metadata block so no need to escape colon\n * $localize `:some description::this message starts with a colon (:)`;\n * // no metadata block so the colon must be escaped\n * $localize `\\:this message starts with a colon (:)`;\n * ```\n *\n * ```ts\n * // named substitution so no need to escape colon\n * $localize `${label}:label:: ${}`\n * // anonymous substitution so colon must be escaped\n * $localize `${label}\\: ${}`\n * ```\n *\n * **Processing localized strings:**\n *\n * There are three scenarios:\n *\n * * **compile-time inlining**: the `$localize` tag is transformed at compile time by a\n * transpiler, removing the tag and replacing the template literal string with a translated\n * literal string from a collection of translations provided to the transpilation tool.\n *\n * * **run-time evaluation**: the `$localize` tag is a run-time function that replaces and\n * reorders the parts (static strings and expressions) of the template literal string with strings\n * from a collection of translations loaded at run-time.\n *\n * * **pass-through evaluation**: the `$localize` tag is a run-time function that simply evaluates\n * the original template literal string without applying any translations to the parts. This\n * version is used during development or where there is no need to translate the localized\n * template literals.\n *\n * @param messageParts a collection of the static parts of the template string.\n * @param expressions a collection of the values of each placeholder in the template string.\n * @returns the translated string, with the `messageParts` and `expressions` interleaved together.\n *\n * @publicApi\n */\nconst $localize = function (messageParts, ...expressions) {\n    if ($localize.translate) {\n        // Don't use array expansion here to avoid the compiler adding `__read()` helper unnecessarily.\n        const translation = $localize.translate(messageParts, expressions);\n        messageParts = translation[0];\n        expressions = translation[1];\n    }\n    let message = stripBlock(messageParts[0], messageParts.raw[0]);\n    for (let i = 1; i < messageParts.length; i++) {\n        message += expressions[i - 1] + stripBlock(messageParts[i], messageParts.raw[i]);\n    }\n    return message;\n};\nconst BLOCK_MARKER = ':';\n/**\n * Strip a delimited \"block\" from the start of the `messagePart`, if it is found.\n *\n * If a marker character (:) actually appears in the content at the start of a tagged string or\n * after a substitution expression, where a block has not been provided the character must be\n * escaped with a backslash, `\\:`. This function checks for this by looking at the `raw`\n * messagePart, which should still contain the backslash.\n *\n * @param messagePart The cooked message part to process.\n * @param rawMessagePart The raw message part to check.\n * @returns the message part with the placeholder name stripped, if found.\n * @throws an error if the block is unterminated\n */\nfunction stripBlock(messagePart, rawMessagePart) {\n    return rawMessagePart.charAt(0) === BLOCK_MARKER\n        ? messagePart.substring(findEndOfBlock(messagePart, rawMessagePart) + 1)\n        : messagePart;\n}\n\nexport { $localize, BLOCK_MARKER$1 as BLOCK_MARKER, computeMsgId, findEndOfBlock, parseMessage, parseMetadata, splitBlock };\n\n", "/**\n * @license Angular v20.0.5\n * (c) 2010-2025 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport { $localize } from './localize2.mjs';\n\n// Attach $localize to the global context, as a side-effect of this module.\nglobalThis.$localize = $localize;\n\nexport { $localize };\n\n"], "mappings": ";;;AAkBA,IAAM,iBAAiB;AAoKvB,IAAI;AAAA,CACH,SAAUA,SAAQ;AACf,EAAAA,QAAOA,QAAO,QAAQ,IAAI,CAAC,IAAI;AAC/B,EAAAA,QAAOA,QAAO,KAAK,IAAI,CAAC,IAAI;AAChC,GAAG,WAAW,SAAS,CAAC,EAAE;AAyK1B,SAAS,eAAe,QAAQ,KAAK;AACjC,WAAS,cAAc,GAAG,WAAW,GAAG,cAAc,OAAO,QAAQ,eAAe,YAAY;AAC5F,QAAI,IAAI,QAAQ,MAAM,MAAM;AACxB;AAAA,IACJ,WACS,OAAO,WAAW,MAAM,gBAAgB;AAC7C,aAAO;AAAA,IACX;AAAA,EACJ;AACA,QAAM,IAAI,MAAM,6CAA6C,GAAG,IAAI;AACxE;AAgGA,IAAM,YAAY,SAAU,iBAAiB,aAAa;AACtD,MAAI,UAAU,WAAW;AAErB,UAAM,cAAc,UAAU,UAAU,cAAc,WAAW;AACjE,mBAAe,YAAY,CAAC;AAC5B,kBAAc,YAAY,CAAC;AAAA,EAC/B;AACA,MAAI,UAAU,WAAW,aAAa,CAAC,GAAG,aAAa,IAAI,CAAC,CAAC;AAC7D,WAAS,IAAI,GAAG,IAAI,aAAa,QAAQ,KAAK;AAC1C,eAAW,YAAY,IAAI,CAAC,IAAI,WAAW,aAAa,CAAC,GAAG,aAAa,IAAI,CAAC,CAAC;AAAA,EACnF;AACA,SAAO;AACX;AACA,IAAM,eAAe;AAcrB,SAAS,WAAW,aAAa,gBAAgB;AAC7C,SAAO,eAAe,OAAO,CAAC,MAAM,eAC9B,YAAY,UAAU,eAAe,aAAa,cAAc,IAAI,CAAC,IACrE;AACV;;;ACneA,WAAW,YAAY;", "names": ["<PERSON><PERSON>"]}