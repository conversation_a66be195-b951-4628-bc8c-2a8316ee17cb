{"version": 3, "file": "ff-GN.js", "sourceRoot": "", "sources": ["ff-GN.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,+FAA+F;AAC/F,+FAA+F;AAC/F,4FAA4F;AAC5F,gGAAgG;AAChG,gGAAgG;AAChG,iFAAiF;AAEjF,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,EAAE,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// **Note**: Locale files are generated through Bazel and never part of the sources. This is an\n// exception for backwards compatibility. With the Gulp setup we never deleted old locale files\n// when updating CLDR, so older locale files which have been removed, or renamed in the CLDR\n// data remained in the repository. We keep these files checked-in until the next major to avoid\n// potential breaking changes. It's worth noting that the locale data for such files is outdated\n// anyway. e.g. the data is missing the directionality, throwing off the indices.\n\nconst u = undefined;\n\nexport default [];\n"]}