# Project Structure Documentation

## Overview
This document outlines the structure and organization of the Armored Doors Manufacturing Management System.

## Solution Structure

```
ArmoredDoorsManagement/
├── ArmoredDoorsManagement.sln          # Main solution file
├── README.md                           # Project overview and setup instructions
├── docker-compose.yml                  # Docker configuration for development/production
├── setup-dev.ps1                      # Development environment setup script
├── .gitignore                          # Git ignore rules
│
├── src/                                # Source code directory
│   ├── Backend/                        # .NET Core backend projects
│   │   ├── ArmoredDoors.API/          # Web API project (Controllers, Middleware, Configuration)
│   │   ├── ArmoredDoors.Core/         # Domain models, entities, and interfaces
│   │   ├── ArmoredDoors.Application/  # Business logic, services, and use cases
│   │   ├── ArmoredDoors.Infrastructure/ # Data access, external services, repositories
│   │   └── ArmoredDoors.Shared/       # Shared DTOs, utilities, and common code
│   │
│   ├── Frontend/                       # Angular frontend application
│   │   └── armored-doors-app/         # Angular project with components, services, modules
│   │
│   ├── Database/                       # Database-related files
│   │   ├── Scripts/                   # SQL scripts for database setup and maintenance
│   │   └── SeedData/                  # Initial data and test data scripts
│   │
│   └── Tests/                         # Test projects
│       ├── ArmoredDoors.UnitTests/    # Unit tests for business logic
│       └── ArmoredDoors.IntegrationTests/ # Integration tests for API endpoints
│
└── docs/                              # Documentation
    ├── project-structure.md          # This file
    ├── api-documentation.md          # API endpoints documentation
    ├── database-schema.md            # Database design documentation
    └── deployment-guide.md           # Deployment instructions
```

## Backend Architecture (Clean Architecture)

### ArmoredDoors.Core
- **Entities**: Domain models representing business objects
- **Interfaces**: Repository and service contracts
- **Enums**: Business-related enumerations
- **Value Objects**: Immutable objects representing domain concepts

### ArmoredDoors.Application
- **Services**: Business logic implementation
- **DTOs**: Data Transfer Objects for API communication
- **Validators**: Input validation logic
- **Mappings**: AutoMapper profiles
- **Use Cases**: Application-specific business rules

### ArmoredDoors.Infrastructure
- **Data**: Entity Framework DbContext and configurations
- **Repositories**: Data access implementations
- **External Services**: Third-party integrations
- **Caching**: Redis implementation
- **SignalR**: Real-time communication hubs

### ArmoredDoors.API
- **Controllers**: API endpoints
- **Middleware**: Custom middleware components
- **Configuration**: Startup and service configuration
- **Filters**: Action filters and exception handling

### ArmoredDoors.Shared
- **DTOs**: Shared data transfer objects
- **Constants**: Application constants
- **Extensions**: Utility extension methods
- **Helpers**: Common helper classes

## Frontend Architecture (Angular)

### Core Structure
```
src/
├── app/
│   ├── core/                          # Singleton services, guards, interceptors
│   ├── shared/                        # Shared components, pipes, directives
│   ├── features/                      # Feature modules
│   │   ├── auth/                      # Authentication module
│   │   ├── dashboard/                 # Dashboard module
│   │   ├── partners/                  # Partner management module
│   │   ├── warehouse/                 # Warehouse management module
│   │   ├── financial/                 # Financial management module
│   │   └── reports/                   # Reporting module
│   ├── layouts/                       # Application layouts
│   └── assets/                        # Static assets (images, icons, etc.)
```

## Key Design Patterns

### Backend
- **Clean Architecture**: Separation of concerns with dependency inversion
- **Repository Pattern**: Data access abstraction
- **Unit of Work**: Transaction management
- **CQRS**: Command Query Responsibility Segregation for complex operations
- **Mediator Pattern**: Decoupled request/response handling

### Frontend
- **Feature Modules**: Organized by business functionality
- **Lazy Loading**: Performance optimization
- **State Management**: NgRx for complex state scenarios
- **Reactive Programming**: RxJS for asynchronous operations

## Technology Stack

### Backend
- **.NET 8.0**: Latest LTS version
- **Entity Framework Core**: ORM for data access
- **AutoMapper**: Object-to-object mapping
- **FluentValidation**: Input validation
- **Serilog**: Structured logging
- **SignalR**: Real-time communication
- **Redis**: Caching and session management
- **JWT**: Authentication and authorization

### Frontend
- **Angular 17+**: Latest version with standalone components
- **TypeScript**: Type-safe JavaScript
- **Tailwind CSS**: Utility-first CSS framework
- **Angular Material**: UI component library
- **NgRx**: State management
- **Chart.js**: Data visualization
- **SignalR Client**: Real-time communication

### Database
- **SQL Server**: Primary database
- **Entity Framework Migrations**: Database versioning

### DevOps
- **Docker**: Containerization
- **Docker Compose**: Multi-container orchestration
- **GitHub Actions**: CI/CD (future implementation)

## Development Workflow

1. **Backend Development**
   - Create entities in Core project
   - Implement repositories in Infrastructure
   - Add business logic in Application
   - Expose APIs in API project
   - Write tests in Test projects

2. **Frontend Development**
   - Create feature modules
   - Implement components and services
   - Add routing and guards
   - Integrate with backend APIs
   - Add responsive design

3. **Database Changes**
   - Update entity models
   - Create EF migrations
   - Update seed data if needed
   - Test migration scripts

## Security Considerations

- **Authentication**: JWT-based with refresh tokens
- **Authorization**: Role-based access control (RBAC)
- **Data Protection**: Encryption at rest and in transit
- **Input Validation**: Server-side validation for all inputs
- **CORS**: Configured for specific origins
- **Rate Limiting**: API throttling to prevent abuse

## Performance Optimization

- **Caching**: Redis for frequently accessed data
- **Database Indexing**: Optimized queries with proper indexes
- **Lazy Loading**: Frontend modules loaded on demand
- **Compression**: Response compression enabled
- **CDN**: Static assets served from CDN (production)

## Monitoring and Logging

- **Structured Logging**: Serilog with JSON formatting
- **Application Insights**: Performance monitoring (Azure)
- **Health Checks**: API health monitoring endpoints
- **Error Tracking**: Centralized error logging and alerting
