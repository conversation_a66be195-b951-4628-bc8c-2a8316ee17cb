import { Component, OnInit, On<PERSON><PERSON>roy, HostListener } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterOutlet } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LanguageService } from '../../core/services/language';
import { AuthService } from '../../core/services/auth';
import { ThemeService } from '../../core/services/theme';
import { SidebarComponent } from '../sidebar/sidebar';
import { NavbarComponent } from '../navbar/navbar';

@Component({
  selector: 'app-main-layout',
  standalone: true,
  imports: [CommonModule, RouterOutlet, SidebarComponent, NavbarComponent],
  templateUrl: './main-layout.html',
  styleUrl: './main-layout.scss'
})
export class MainLayoutComponent implements OnInit, OnDestroy {
  isSidebarOpen = false;
  isMobile = false;
  isTablet = false;
  isDesktop = false;

  private destroy$ = new Subject<void>();

  constructor(
    private languageService: LanguageService,
    private authService: AuthService,
    private themeService: ThemeService
  ) {
    this.checkScreenSize();
  }

  ngOnInit(): void {
    // Subscribe to language changes for RTL/LTR support
    this.languageService.currentLanguage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        // Language direction is handled by the language service
      });

    // Initialize theme service (this ensures theme is applied on app start)
    this.themeService.getCurrentTheme();

    // Set initial sidebar state based on screen size
    this.setSidebarInitialState();
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  @HostListener('window:resize', ['$event'])
  onResize(): void {
    this.checkScreenSize();
    this.setSidebarInitialState();
  }

  private checkScreenSize(): void {
    const width = window.innerWidth;
    this.isMobile = width < 768;
    this.isTablet = width >= 768 && width < 1024;
    this.isDesktop = width >= 1024;
  }

  private setSidebarInitialState(): void {
    // Auto-close sidebar on mobile, keep open on desktop
    if (this.isMobile) {
      this.isSidebarOpen = false;
    } else if (this.isDesktop) {
      this.isSidebarOpen = true;
    }
    // On tablet, keep current state
  }

  toggleSidebar(): void {
    this.isSidebarOpen = !this.isSidebarOpen;
  }

  closeSidebar(): void {
    this.isSidebarOpen = false;
  }

  onSidebarBackdropClick(): void {
    if (this.isMobile || this.isTablet) {
      this.closeSidebar();
    }
  }

  get isRTL(): boolean {
    return this.languageService.isRTL();
  }

  get currentLanguage() {
    return this.languageService.getCurrentLanguage();
  }
}
