<?xml version="1.0" encoding="utf-8"?>
<root>
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" msdata:Ordinal="5" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  
  <!-- Authentication Messages -->
  <data name="InvalidCredentials" xml:space="preserve">
    <value>Invalid username or password</value>
  </data>
  <data name="LoginSuccessful" xml:space="preserve">
    <value>Login successful</value>
  </data>
  <data name="TokenRefreshed" xml:space="preserve">
    <value>Token refreshed successfully</value>
  </data>
  <data name="TokenRevoked" xml:space="preserve">
    <value>Token revoked successfully</value>
  </data>
  
  <!-- User Management -->
  <data name="UserCreated" xml:space="preserve">
    <value>User created successfully</value>
  </data>
  <data name="UserUpdated" xml:space="preserve">
    <value>User updated successfully</value>
  </data>
  <data name="UserDeleted" xml:space="preserve">
    <value>User deleted successfully</value>
  </data>
  <data name="UserNotFound" xml:space="preserve">
    <value>User not found</value>
  </data>
  <data name="UsernameExists" xml:space="preserve">
    <value>Username already exists</value>
  </data>
  <data name="EmailExists" xml:space="preserve">
    <value>Email already exists</value>
  </data>
  
  <!-- Validation Messages -->
  <data name="RequiredField" xml:space="preserve">
    <value>{0} is required</value>
  </data>
  <data name="InvalidEmail" xml:space="preserve">
    <value>Invalid email format</value>
  </data>
  <data name="InvalidPhoneNumber" xml:space="preserve">
    <value>Invalid phone number format</value>
  </data>
  <data name="PasswordTooShort" xml:space="preserve">
    <value>Password must be at least {0} characters long</value>
  </data>
  
  <!-- General Messages -->
  <data name="Success" xml:space="preserve">
    <value>Operation completed successfully</value>
  </data>
  <data name="Error" xml:space="preserve">
    <value>An error occurred</value>
  </data>
  <data name="InsufficientPermissions" xml:space="preserve">
    <value>Insufficient permissions to perform this action</value>
  </data>
  <data name="NotFound" xml:space="preserve">
    <value>Resource not found</value>
  </data>
  
  <!-- Company Information -->
  <data name="CompanyName" xml:space="preserve">
    <value>Armored Doors Manufacturing Company</value>
  </data>
  <data name="CompanyAddress" xml:space="preserve">
    <value>123 Industrial Street, Manufacturing District</value>
  </data>
  <data name="CompanyPhone" xml:space="preserve">
    <value>******-0123</value>
  </data>
  <data name="CompanyEmail" xml:space="preserve">
    <value><EMAIL></value>
  </data>
</root>
