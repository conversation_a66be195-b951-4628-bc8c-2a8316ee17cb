using Microsoft.EntityFrameworkCore.Storage;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Interfaces;
using ArmoredDoors.Infrastructure.Data;

namespace ArmoredDoors.Infrastructure.Repositories;

public class UnitOfWork : IUnitOfWork
{
    private readonly ArmoredDoorsDbContext _context;
    private IDbContextTransaction? _transaction;

    // Repository instances
    private IUserRepository? _users;
    private IRepository<Role>? _roles;
    private IRepository<Permission>? _permissions;
    private IPartnerRepository? _partners;
    private ICapitalTransactionRepository? _capitalTransactions;
    private IRepository<ProfitDistribution>? _profitDistributions;
    private IRepository<Category>? _categories;
    private IRepository<MeasurementUnit>? _measurementUnits;
    private IRepository<StorageLocation>? _storageLocations;
    private IRawMaterialRepository? _rawMaterials;
    private IFinishedProductRepository? _finishedProducts;
    private IInventoryMovementRepository? _inventoryMovements;
    private ICustomerRepository? _customers;
    private ISupplierRepository? _suppliers;
    private IRepository<Department>? _departments;
    private IRepository<Employee>? _employees;
    private IRepository<FinancialAccount>? _financialAccounts;
    private IFinancialTransactionRepository? _financialTransactions;
    private IRepository<SystemSetting>? _systemSettings;
    private IRepository<AuditLog>? _auditLogs;

    public UnitOfWork(ArmoredDoorsDbContext context)
    {
        _context = context;
    }

    public IUserRepository Users => _users ??= new UserRepository(_context);

    public IRepository<Role> Roles => _roles ??= new Repository<Role>(_context);

    public IRepository<Permission> Permissions => _permissions ??= new Repository<Permission>(_context);

    public IPartnerRepository Partners => _partners ??= new PartnerRepository(_context);

    public ICapitalTransactionRepository CapitalTransactions => _capitalTransactions ??= new CapitalTransactionRepository(_context);

    public IRepository<ProfitDistribution> ProfitDistributions => _profitDistributions ??= new Repository<ProfitDistribution>(_context);

    public IRepository<Category> Categories => _categories ??= new Repository<Category>(_context);

    public IRepository<MeasurementUnit> MeasurementUnits => _measurementUnits ??= new Repository<MeasurementUnit>(_context);

    public IRepository<StorageLocation> StorageLocations => _storageLocations ??= new Repository<StorageLocation>(_context);

    public IRawMaterialRepository RawMaterials => _rawMaterials ??= new RawMaterialRepository(_context);

    public IFinishedProductRepository FinishedProducts => _finishedProducts ??= new FinishedProductRepository(_context);

    public IInventoryMovementRepository InventoryMovements => _inventoryMovements ??= new InventoryMovementRepository(_context);

    public ICustomerRepository Customers => _customers ??= new CustomerRepository(_context);

    public ISupplierRepository Suppliers => _suppliers ??= new SupplierRepository(_context);

    public IRepository<Department> Departments => _departments ??= new Repository<Department>(_context);

    public IRepository<Employee> Employees => _employees ??= new Repository<Employee>(_context);

    public IRepository<FinancialAccount> FinancialAccounts => _financialAccounts ??= new Repository<FinancialAccount>(_context);

    public IFinancialTransactionRepository FinancialTransactions => _financialTransactions ??= new FinancialTransactionRepository(_context);

    public IRepository<SystemSetting> SystemSettings => _systemSettings ??= new Repository<SystemSetting>(_context);

    public IRepository<AuditLog> AuditLogs => _auditLogs ??= new Repository<AuditLog>(_context);

    public async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        return await _context.SaveChangesAsync(cancellationToken);
    }

    public async Task BeginTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction != null)
        {
            throw new InvalidOperationException("A transaction is already in progress.");
        }

        _transaction = await _context.Database.BeginTransactionAsync(cancellationToken);
    }

    public async Task CommitTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("No transaction in progress.");
        }

        try
        {
            await _context.SaveChangesAsync(cancellationToken);
            await _transaction.CommitAsync(cancellationToken);
        }
        catch
        {
            await _transaction.RollbackAsync(cancellationToken);
            throw;
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public async Task RollbackTransactionAsync(CancellationToken cancellationToken = default)
    {
        if (_transaction == null)
        {
            throw new InvalidOperationException("No transaction in progress.");
        }

        try
        {
            await _transaction.RollbackAsync(cancellationToken);
        }
        finally
        {
            await _transaction.DisposeAsync();
            _transaction = null;
        }
    }

    public void Dispose()
    {
        _transaction?.Dispose();
        _context.Dispose();
    }
}
