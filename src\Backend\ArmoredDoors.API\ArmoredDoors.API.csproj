<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net9.0</TargetFramework>
    <Nullable>enable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>
    <GenerateDocumentationFile>true</GenerateDocumentationFile>
    <DocumentationFile>bin\Debug\net9.0\ArmoredDoors.API.xml</DocumentationFile>
    <NoWarn>$(NoWarn);1591</NoWarn>
  </PropertyGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="9.0.6" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="9.0.3" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Tools" Version="9.0.6">
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
      <PrivateAssets>all</PrivateAssets>
    </PackageReference>
    <PackageReference Include="Microsoft.Extensions.Localization" Version="9.0.6" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="9.0.1" />
    <PackageReference Include="Swashbuckle.AspNetCore.Annotations" Version="9.0.1" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\ArmoredDoors.Application\ArmoredDoors.Application.csproj" />
    <ProjectReference Include="..\ArmoredDoors.Infrastructure\ArmoredDoors.Infrastructure.csproj" />
  </ItemGroup>

</Project>
