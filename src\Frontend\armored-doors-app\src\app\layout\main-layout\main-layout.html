<div class="app-layout" [attr.dir]="currentLanguage.direction" [class.rtl]="isRTL">
  <!-- Mobile Sidebar Backdrop -->
  <div
    *ngIf="isSidebarOpen && (isMobile || isTablet)"
    class="fixed inset-0 z-40 bg-black bg-opacity-50 transition-opacity duration-300 lg:hidden"
    (click)="onSidebarBackdropClick()"
    aria-hidden="true">
  </div>

  <!-- Sidebar -->
  <div class="sidebar-container"
       [class.sidebar-open]="isSidebarOpen"
       [class.sidebar-closed]="!isSidebarOpen">
    <app-sidebar
      [isOpen]="isSidebarOpen"
      [isMobile]="isMobile"
      [isTablet]="isTablet"
      [isDesktop]="isDesktop"
      (closeSidebar)="closeSidebar()">
    </app-sidebar>
  </div>

  <!-- Main Content Area -->
  <div class="main-content"
       [class.sidebar-open]="isSidebarOpen && isDesktop"
       [class.sidebar-closed]="!isSidebarOpen || !isDesktop">

    <!-- Top Navigation Bar -->
    <app-navbar
      [isSidebarOpen]="isSidebarOpen"
      [isMobile]="isMobile"
      [isTablet]="isTablet"
      [isDesktop]="isDesktop"
      (toggleSidebar)="toggleSidebar()">
    </app-navbar>

    <!-- Page Content -->
    <main class="page-content">
      <div class="content-wrapper">
        <router-outlet></router-outlet>
      </div>
    </main>
  </div>
</div>
