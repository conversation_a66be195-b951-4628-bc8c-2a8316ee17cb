// Theme Toggle Component Styles
.theme-toggle-btn {
  @apply p-2 rounded-lg transition-all duration-200 focus:outline-none focus:ring-2 focus:ring-primary-500 focus:ring-opacity-50;

  // Light mode styles
  @apply text-gray-600 hover:text-gray-900 hover:bg-gray-100;

  // Dark mode styles
  @apply dark:text-gray-300 dark:hover:text-white dark:hover:bg-gray-700;

  &.quick-toggle {
    @apply relative;

    // Add subtle animation for theme switching
    &:active {
      @apply scale-95;
    }
  }

  &.dropdown-toggle {
    @apply flex items-center space-x-2;

    [dir="rtl"] & {
      @apply space-x-reverse;
    }
  }
}

// Dropdown Menu
.theme-dropdown {
  @apply absolute right-0 mt-2 w-48 bg-white dark:bg-gray-800 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-1 z-50;

  // Animation
  animation: dropdownFadeIn 0.15s ease-out;

  &.dropdown-rtl {
    @apply right-auto left-0;
  }

  // Responsive adjustments
  @media (max-width: 767px) {
    @apply w-40;
  }
}

// Theme Options
.theme-option {
  @apply w-full px-4 py-3 text-left text-sm transition-colors duration-150 flex items-center;

  // Light mode
  @apply text-gray-700 hover:bg-gray-100;

  // Dark mode
  @apply dark:text-gray-200 dark:hover:bg-gray-700;

  // Active state
  &.active {
    @apply bg-primary-50 text-primary-700 dark:bg-primary-900/20 dark:text-primary-300;
  }

  // Focus state
  &:focus {
    @apply outline-none bg-gray-100 dark:bg-gray-700;
  }

  // RTL support
  [dir="rtl"] & {
    @apply text-right;

    .flex {
      @apply space-x-reverse;
    }
  }

  // Icon styling
  svg {
    @apply transition-colors duration-150;
  }

  // Responsive text
  @media (max-width: 767px) {
    @apply px-3 py-2 text-xs;
  }
}

// Animations
@keyframes dropdownFadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

// Smooth theme transition for the component itself
:host {
  @apply transition-colors duration-300;
}

// High contrast mode support
@media (prefers-contrast: high) {
  .theme-toggle-btn {
    @apply border border-gray-300 dark:border-gray-600;

    &:hover {
      @apply border-gray-900 dark:border-gray-100;
    }
  }

  .theme-dropdown {
    @apply border-2 border-gray-900 dark:border-gray-100;
  }

  .theme-option {
    &.active {
      @apply border-l-4 border-primary-600 dark:border-primary-400;

      [dir="rtl"] & {
        @apply border-l-0 border-r-4;
      }
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .theme-toggle-btn,
  .theme-option,
  .theme-dropdown {
    @apply transition-none;
  }

  .theme-dropdown {
    animation: none;
  }

  .theme-toggle-btn svg {
    @apply transition-none;
  }
}