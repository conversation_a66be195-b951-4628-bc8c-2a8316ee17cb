using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ArmoredDoors.Core.Interfaces;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Constants;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace ArmoredDoors.API.Controllers;

/// <summary>
/// User management controller for CRUD operations on system users
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Authorize]
[Produces("application/json")]
[SwaggerTag("User management operations including CRUD, role assignment, and user administration")]
public class UsersController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public UsersController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    /// <summary>
    /// Retrieves a paginated list of system users
    /// </summary>
    /// <param name="page">Page number (default: 1)</param>
    /// <param name="pageSize">Number of items per page (default: 10, max: 100)</param>
    /// <returns>Paginated list of users with their basic information and roles</returns>
    /// <response code="200">Successfully retrieved users list</response>
    /// <response code="403">Insufficient permissions to view users</response>
    [HttpGet]
    [SwaggerOperation(
        Summary = "Get Users List",
        Description = "Retrieves a paginated list of active system users with their basic information and assigned roles. Requires 'Users.View' permission."
    )]
    [SwaggerResponse(200, "Successfully retrieved users list", typeof(PaginatedUsersResponse))]
    [SwaggerResponse(403, "Insufficient permissions to view users")]
    public async Task<IActionResult> GetUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        // Check if user has permission to view users
        if (!HasPermission(Permissions.Users.View))
        {
            return Forbid();
        }

        var (users, totalCount) = await _unitOfWork.Users.GetPagedAsync(
            page, 
            pageSize, 
            predicate: u => u.IsActive,
            orderBy: u => u.Username,
            includes: u => u.UserRoles
        );

        var userDtos = users.Select(u => new UserDto
        {
            Id = u.Id,
            Username = u.Username,
            Email = u.Email,
            FirstName = u.FirstName,
            LastName = u.LastName,
            FullName = u.FullName,
            PhoneNumber = u.PhoneNumber,
            IsActive = u.IsActive,
            LastLoginDate = u.LastLoginDate,
            Roles = u.UserRoles.Select(ur => ur.Role.Name).ToList(),
            CreatedDate = u.CreatedDate
        });

        return Ok(new
        {
            users = userDtos,
            totalCount,
            page,
            pageSize,
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        });
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetUser(Guid id)
    {
        if (!HasPermission(Permissions.Users.View))
        {
            return Forbid();
        }

        var user = await _unitOfWork.Users.GetByIdAsync(id, u => u.UserRoles);
        
        if (user == null)
        {
            return NotFound();
        }

        var userDto = new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber,
            IsActive = user.IsActive,
            LastLoginDate = user.LastLoginDate,
            Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList(),
            CreatedDate = user.CreatedDate
        };

        return Ok(userDto);
    }

    [HttpPost]
    public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
    {
        if (!HasPermission(Permissions.Users.Create))
        {
            return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Check if username is unique
        if (!await _unitOfWork.Users.IsUsernameUniqueAsync(request.Username))
        {
            return BadRequest(new { message = "Username already exists" });
        }

        // Check if email is unique
        if (!await _unitOfWork.Users.IsEmailUniqueAsync(request.Email))
        {
            return BadRequest(new { message = "Email already exists" });
        }

        var user = new User
        {
            Username = request.Username,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PhoneNumber = request.PhoneNumber,
            PasswordHash = HashPassword(request.Password), // In production, use proper password hashing
            IsActive = true,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = GetCurrentUserId()
        };

        await _unitOfWork.Users.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return CreatedAtAction(nameof(GetUser), new { id = user.Id }, new { id = user.Id });
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserRequest request)
    {
        if (!HasPermission(Permissions.Users.Edit))
        {
            return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var user = await _unitOfWork.Users.GetByIdAsync(id);
        
        if (user == null)
        {
            return NotFound();
        }

        // Check if username is unique (excluding current user)
        if (!await _unitOfWork.Users.IsUsernameUniqueAsync(request.Username, id))
        {
            return BadRequest(new { message = "Username already exists" });
        }

        // Check if email is unique (excluding current user)
        if (!await _unitOfWork.Users.IsEmailUniqueAsync(request.Email, id))
        {
            return BadRequest(new { message = "Email already exists" });
        }

        user.Username = request.Username;
        user.Email = request.Email;
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber;
        user.IsActive = request.IsActive;
        user.ModifiedDate = DateTime.UtcNow;
        user.ModifiedBy = GetCurrentUserId();

        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        if (!HasPermission(Permissions.Users.Delete))
        {
            return Forbid();
        }

        var user = await _unitOfWork.Users.GetByIdAsync(id);
        
        if (user == null)
        {
            return NotFound();
        }

        // Soft delete by setting IsActive to false
        user.IsActive = false;
        user.ModifiedDate = DateTime.UtcNow;
        user.ModifiedBy = GetCurrentUserId();

        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    private bool HasPermission(string permission)
    {
        return User.HasClaim("permission", permission);
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
        return userIdClaim != null ? Guid.Parse(userIdClaim.Value) : null;
    }

    private static string HashPassword(string password)
    {
        // This is a simplified password hashing - use proper hashing in production
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + "ArmoredDoorsSalt"));
        return Convert.ToBase64String(hashedBytes);
    }
}

/// <summary>
/// User data transfer object containing user information
/// </summary>
public class UserDto
{
    /// <summary>
    /// Unique identifier for the user
    /// </summary>
    [SwaggerSchema("Unique identifier for the user")]
    public Guid Id { get; set; }

    /// <summary>
    /// Username for login
    /// </summary>
    [SwaggerSchema("Username for login")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// User's email address
    /// </summary>
    [SwaggerSchema("User's email address")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// User's first name
    /// </summary>
    [SwaggerSchema("User's first name")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// User's last name
    /// </summary>
    [SwaggerSchema("User's last name")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// User's full name (first + last)
    /// </summary>
    [SwaggerSchema("User's full name (first + last)")]
    public string FullName { get; set; } = string.Empty;

    /// <summary>
    /// User's phone number (optional)
    /// </summary>
    [SwaggerSchema("User's phone number (optional)")]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// </summary>
    [SwaggerSchema("Whether the user account is active")]
    public bool IsActive { get; set; }

    /// <summary>
    /// Last login date and time
    /// </summary>
    [SwaggerSchema("Last login date and time")]
    public DateTime? LastLoginDate { get; set; }

    /// <summary>
    /// List of roles assigned to the user
    /// </summary>
    [SwaggerSchema("List of roles assigned to the user")]
    public List<string> Roles { get; set; } = new();

    /// <summary>
    /// Date when the user was created
    /// </summary>
    [SwaggerSchema("Date when the user was created")]
    public DateTime CreatedDate { get; set; }
}

/// <summary>
/// Paginated response for users list
/// </summary>
public class PaginatedUsersResponse
{
    /// <summary>
    /// List of users for the current page
    /// </summary>
    [SwaggerSchema("List of users for the current page")]
    public IEnumerable<UserDto> Users { get; set; } = new List<UserDto>();

    /// <summary>
    /// Total number of users
    /// </summary>
    [SwaggerSchema("Total number of users")]
    public int TotalCount { get; set; }

    /// <summary>
    /// Current page number
    /// </summary>
    [SwaggerSchema("Current page number")]
    public int Page { get; set; }

    /// <summary>
    /// Number of items per page
    /// </summary>
    [SwaggerSchema("Number of items per page")]
    public int PageSize { get; set; }

    /// <summary>
    /// Total number of pages
    /// </summary>
    [SwaggerSchema("Total number of pages")]
    public int TotalPages { get; set; }
}

/// <summary>
/// Request model for creating a new user
/// </summary>
public class CreateUserRequest
{
    /// <summary>
    /// Username for the new user (must be unique)
    /// </summary>
    /// <example>john.doe</example>
    [Required(ErrorMessage = "Username is required")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Username must be between 3 and 50 characters")]
    [SwaggerSchema("Username for the new user (must be unique)")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address for the new user (must be unique)
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [SwaggerSchema("Email address for the new user (must be unique)")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// First name of the user
    /// </summary>
    /// <example>John</example>
    [Required(ErrorMessage = "First name is required")]
    [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    [SwaggerSchema("First name of the user")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Last name of the user
    /// </summary>
    /// <example>Doe</example>
    [Required(ErrorMessage = "Last name is required")]
    [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    [SwaggerSchema("Last name of the user")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Phone number of the user (optional)
    /// </summary>
    /// <example>******-0123</example>
    [Phone(ErrorMessage = "Invalid phone number format")]
    [SwaggerSchema("Phone number of the user (optional)")]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Password for the new user
    /// </summary>
    /// <example>SecurePassword123!</example>
    [Required(ErrorMessage = "Password is required")]
    [StringLength(100, MinimumLength = 8, ErrorMessage = "Password must be between 8 and 100 characters")]
    [SwaggerSchema("Password for the new user")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Request model for updating an existing user
/// </summary>
public class UpdateUserRequest
{
    /// <summary>
    /// Username for the user (must be unique)
    /// </summary>
    /// <example>john.doe</example>
    [Required(ErrorMessage = "Username is required")]
    [StringLength(50, MinimumLength = 3, ErrorMessage = "Username must be between 3 and 50 characters")]
    [SwaggerSchema("Username for the user (must be unique)")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Email address for the user (must be unique)
    /// </summary>
    /// <example><EMAIL></example>
    [Required(ErrorMessage = "Email is required")]
    [EmailAddress(ErrorMessage = "Invalid email format")]
    [SwaggerSchema("Email address for the user (must be unique)")]
    public string Email { get; set; } = string.Empty;

    /// <summary>
    /// First name of the user
    /// </summary>
    /// <example>John</example>
    [Required(ErrorMessage = "First name is required")]
    [StringLength(50, ErrorMessage = "First name cannot exceed 50 characters")]
    [SwaggerSchema("First name of the user")]
    public string FirstName { get; set; } = string.Empty;

    /// <summary>
    /// Last name of the user
    /// </summary>
    /// <example>Doe</example>
    [Required(ErrorMessage = "Last name is required")]
    [StringLength(50, ErrorMessage = "Last name cannot exceed 50 characters")]
    [SwaggerSchema("Last name of the user")]
    public string LastName { get; set; } = string.Empty;

    /// <summary>
    /// Phone number of the user (optional)
    /// </summary>
    /// <example>******-0123</example>
    [Phone(ErrorMessage = "Invalid phone number format")]
    [SwaggerSchema("Phone number of the user (optional)")]
    public string? PhoneNumber { get; set; }

    /// <summary>
    /// Whether the user account is active
    /// </summary>
    /// <example>true</example>
    [SwaggerSchema("Whether the user account is active")]
    public bool IsActive { get; set; } = true;
}
