using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using ArmoredDoors.Core.Interfaces;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Constants;

namespace ArmoredDoors.API.Controllers;

[ApiController]
[Route("api/[controller]")]
[Authorize]
public class UsersController : ControllerBase
{
    private readonly IUnitOfWork _unitOfWork;

    public UsersController(IUnitOfWork unitOfWork)
    {
        _unitOfWork = unitOfWork;
    }

    [HttpGet]
    [Authorize(Policy = "RequirePermission")]
    public async Task<IActionResult> GetUsers([FromQuery] int page = 1, [FromQuery] int pageSize = 10)
    {
        // Check if user has permission to view users
        if (!HasPermission(Permissions.Users.View))
        {
            return Forbid();
        }

        var (users, totalCount) = await _unitOfWork.Users.GetPagedAsync(
            page, 
            pageSize, 
            predicate: u => u.IsActive,
            orderBy: u => u.Username,
            includes: u => u.UserRoles
        );

        var userDtos = users.Select(u => new UserDto
        {
            Id = u.Id,
            Username = u.Username,
            Email = u.Email,
            FirstName = u.FirstName,
            LastName = u.LastName,
            FullName = u.FullName,
            PhoneNumber = u.PhoneNumber,
            IsActive = u.IsActive,
            LastLoginDate = u.LastLoginDate,
            Roles = u.UserRoles.Select(ur => ur.Role.Name).ToList(),
            CreatedDate = u.CreatedDate
        });

        return Ok(new
        {
            users = userDtos,
            totalCount,
            page,
            pageSize,
            totalPages = (int)Math.Ceiling((double)totalCount / pageSize)
        });
    }

    [HttpGet("{id}")]
    public async Task<IActionResult> GetUser(Guid id)
    {
        if (!HasPermission(Permissions.Users.View))
        {
            return Forbid();
        }

        var user = await _unitOfWork.Users.GetByIdAsync(id, u => u.UserRoles);
        
        if (user == null)
        {
            return NotFound();
        }

        var userDto = new UserDto
        {
            Id = user.Id,
            Username = user.Username,
            Email = user.Email,
            FirstName = user.FirstName,
            LastName = user.LastName,
            FullName = user.FullName,
            PhoneNumber = user.PhoneNumber,
            IsActive = user.IsActive,
            LastLoginDate = user.LastLoginDate,
            Roles = user.UserRoles.Select(ur => ur.Role.Name).ToList(),
            CreatedDate = user.CreatedDate
        };

        return Ok(userDto);
    }

    [HttpPost]
    public async Task<IActionResult> CreateUser([FromBody] CreateUserRequest request)
    {
        if (!HasPermission(Permissions.Users.Create))
        {
            return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        // Check if username is unique
        if (!await _unitOfWork.Users.IsUsernameUniqueAsync(request.Username))
        {
            return BadRequest(new { message = "Username already exists" });
        }

        // Check if email is unique
        if (!await _unitOfWork.Users.IsEmailUniqueAsync(request.Email))
        {
            return BadRequest(new { message = "Email already exists" });
        }

        var user = new User
        {
            Username = request.Username,
            Email = request.Email,
            FirstName = request.FirstName,
            LastName = request.LastName,
            PhoneNumber = request.PhoneNumber,
            PasswordHash = HashPassword(request.Password), // In production, use proper password hashing
            IsActive = true,
            CreatedDate = DateTime.UtcNow,
            CreatedBy = GetCurrentUserId()
        };

        await _unitOfWork.Users.AddAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return CreatedAtAction(nameof(GetUser), new { id = user.Id }, new { id = user.Id });
    }

    [HttpPut("{id}")]
    public async Task<IActionResult> UpdateUser(Guid id, [FromBody] UpdateUserRequest request)
    {
        if (!HasPermission(Permissions.Users.Edit))
        {
            return Forbid();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var user = await _unitOfWork.Users.GetByIdAsync(id);
        
        if (user == null)
        {
            return NotFound();
        }

        // Check if username is unique (excluding current user)
        if (!await _unitOfWork.Users.IsUsernameUniqueAsync(request.Username, id))
        {
            return BadRequest(new { message = "Username already exists" });
        }

        // Check if email is unique (excluding current user)
        if (!await _unitOfWork.Users.IsEmailUniqueAsync(request.Email, id))
        {
            return BadRequest(new { message = "Email already exists" });
        }

        user.Username = request.Username;
        user.Email = request.Email;
        user.FirstName = request.FirstName;
        user.LastName = request.LastName;
        user.PhoneNumber = request.PhoneNumber;
        user.IsActive = request.IsActive;
        user.ModifiedDate = DateTime.UtcNow;
        user.ModifiedBy = GetCurrentUserId();

        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    [HttpDelete("{id}")]
    public async Task<IActionResult> DeleteUser(Guid id)
    {
        if (!HasPermission(Permissions.Users.Delete))
        {
            return Forbid();
        }

        var user = await _unitOfWork.Users.GetByIdAsync(id);
        
        if (user == null)
        {
            return NotFound();
        }

        // Soft delete by setting IsActive to false
        user.IsActive = false;
        user.ModifiedDate = DateTime.UtcNow;
        user.ModifiedBy = GetCurrentUserId();

        await _unitOfWork.Users.UpdateAsync(user);
        await _unitOfWork.SaveChangesAsync();

        return NoContent();
    }

    private bool HasPermission(string permission)
    {
        return User.HasClaim("permission", permission);
    }

    private Guid? GetCurrentUserId()
    {
        var userIdClaim = User.FindFirst(System.Security.Claims.ClaimTypes.NameIdentifier);
        return userIdClaim != null ? Guid.Parse(userIdClaim.Value) : null;
    }

    private static string HashPassword(string password)
    {
        // This is a simplified password hashing - use proper hashing in production
        using var sha256 = System.Security.Cryptography.SHA256.Create();
        var hashedBytes = sha256.ComputeHash(System.Text.Encoding.UTF8.GetBytes(password + "ArmoredDoorsSalt"));
        return Convert.ToBase64String(hashedBytes);
    }
}

public class UserDto
{
    public Guid Id { get; set; }
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string FullName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public bool IsActive { get; set; }
    public DateTime? LastLoginDate { get; set; }
    public List<string> Roles { get; set; } = new();
    public DateTime CreatedDate { get; set; }
}

public class CreateUserRequest
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public string Password { get; set; } = string.Empty;
}

public class UpdateUserRequest
{
    public string Username { get; set; } = string.Empty;
    public string Email { get; set; } = string.Empty;
    public string FirstName { get; set; } = string.Empty;
    public string LastName { get; set; } = string.Empty;
    public string? PhoneNumber { get; set; }
    public bool IsActive { get; set; } = true;
}
