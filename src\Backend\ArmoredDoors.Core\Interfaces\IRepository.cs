using System.Linq.Expressions;
using ArmoredDoors.Core.Entities;

namespace ArmoredDoors.Core.Interfaces;

public interface IRepository<T> where T : BaseEntity
{
    Task<T?> GetByIdAsync(Guid id, CancellationToken cancellationToken = default);
    Task<T?> GetByIdAsync(Guid id, params Expression<Func<T, object>>[] includes);
    Task<IEnumerable<T>> GetAllAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> GetAllAsync(params Expression<Func<T, object>>[] includes);
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> FindAsync(Expression<Func<T, bool>> predicate, params Expression<Func<T, object>>[] includes);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<T?> FirstOrDefaultAsync(Expression<Func<T, bool>> predicate, params Expression<Func<T, object>>[] includes);
    Task<bool> AnyAsync(Expression<Func<T, bool>> predicate, CancellationToken cancellationToken = default);
    Task<int> CountAsync(Expression<Func<T, bool>>? predicate = null, CancellationToken cancellationToken = default);
    
    Task<T> AddAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> AddRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    
    Task<T> UpdateAsync(T entity, CancellationToken cancellationToken = default);
    Task<IEnumerable<T>> UpdateRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    
    Task DeleteAsync(T entity, CancellationToken cancellationToken = default);
    Task DeleteAsync(Guid id, CancellationToken cancellationToken = default);
    Task DeleteRangeAsync(IEnumerable<T> entities, CancellationToken cancellationToken = default);
    
    // Pagination
    Task<(IEnumerable<T> Items, int TotalCount)> GetPagedAsync(
        int pageNumber, 
        int pageSize, 
        Expression<Func<T, bool>>? predicate = null,
        Expression<Func<T, object>>? orderBy = null,
        bool ascending = true,
        params Expression<Func<T, object>>[] includes);
}

public interface IUserRepository : IRepository<User>
{
    Task<User?> GetByUsernameAsync(string username, CancellationToken cancellationToken = default);
    Task<User?> GetByEmailAsync(string email, CancellationToken cancellationToken = default);
    Task<bool> IsUsernameUniqueAsync(string username, Guid? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<bool> IsEmailUniqueAsync(string email, Guid? excludeUserId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<User>> GetUsersByRoleAsync(string roleName, CancellationToken cancellationToken = default);
}

public interface IPartnerRepository : IRepository<Partner>
{
    Task<IEnumerable<Partner>> GetActivePartnersAsync(CancellationToken cancellationToken = default);
    Task<decimal> GetTotalCapitalAsync(CancellationToken cancellationToken = default);
    Task<IEnumerable<Partner>> GetPartnersWithCapitalTransactionsAsync(DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task UpdateOwnershipPercentagesAsync(CancellationToken cancellationToken = default);
}

public interface ICapitalTransactionRepository : IRepository<CapitalTransaction>
{
    Task<IEnumerable<CapitalTransaction>> GetByPartnerIdAsync(Guid partnerId, CancellationToken cancellationToken = default);
    Task<IEnumerable<CapitalTransaction>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalInjectionsAsync(Guid? partnerId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
    Task<decimal> GetTotalWithdrawalsAsync(Guid? partnerId = null, DateTime? fromDate = null, DateTime? toDate = null, CancellationToken cancellationToken = default);
}

public interface IRawMaterialRepository : IRepository<RawMaterial>
{
    Task<RawMaterial?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<IEnumerable<RawMaterial>> GetByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default);
    Task<IEnumerable<RawMaterial>> GetLowStockItemsAsync(CancellationToken cancellationToken = default);
    Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default);
    Task UpdateStockQuantityAsync(Guid id, decimal quantity, CancellationToken cancellationToken = default);
}

public interface IFinishedProductRepository : IRepository<FinishedProduct>
{
    Task<FinishedProduct?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<FinishedProduct?> GetByBarcodeAsync(string barcode, CancellationToken cancellationToken = default);
    Task<IEnumerable<FinishedProduct>> GetByCategoryAsync(Guid categoryId, CancellationToken cancellationToken = default);
    Task<IEnumerable<FinishedProduct>> GetLowStockItemsAsync(CancellationToken cancellationToken = default);
    Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default);
    Task<bool> IsBarcodeUniqueAsync(string barcode, Guid? excludeId = null, CancellationToken cancellationToken = default);
    Task UpdateStockQuantityAsync(Guid id, decimal quantity, CancellationToken cancellationToken = default);
}

public interface IInventoryMovementRepository : IRepository<InventoryMovement>
{
    Task<IEnumerable<InventoryMovement>> GetByItemAsync(string itemType, Guid itemId, CancellationToken cancellationToken = default);
    Task<IEnumerable<InventoryMovement>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<InventoryMovement>> GetByLocationAsync(Guid locationId, CancellationToken cancellationToken = default);
    Task<IEnumerable<InventoryMovement>> GetByReferenceAsync(string referenceType, Guid referenceId, CancellationToken cancellationToken = default);
}

public interface ICustomerRepository : IRepository<Customer>
{
    Task<Customer?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Customer>> GetByTypeAsync(string customerType, CancellationToken cancellationToken = default);
    Task<IEnumerable<Customer>> GetWithOutstandingBalanceAsync(CancellationToken cancellationToken = default);
}

public interface ISupplierRepository : IRepository<Supplier>
{
    Task<Supplier?> GetByCodeAsync(string code, CancellationToken cancellationToken = default);
    Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default);
    Task<IEnumerable<Supplier>> GetByTypeAsync(string supplierType, CancellationToken cancellationToken = default);
    Task<IEnumerable<Supplier>> GetWithOutstandingBalanceAsync(CancellationToken cancellationToken = default);
}

public interface IFinancialTransactionRepository : IRepository<FinancialTransaction>
{
    Task<FinancialTransaction?> GetByTransactionNumberAsync(string transactionNumber, CancellationToken cancellationToken = default);
    Task<IEnumerable<FinancialTransaction>> GetByEntityAsync(string entityType, Guid entityId, CancellationToken cancellationToken = default);
    Task<IEnumerable<FinancialTransaction>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default);
    Task<IEnumerable<FinancialTransaction>> GetByTypeAsync(string transactionType, CancellationToken cancellationToken = default);
    Task<bool> IsTransactionNumberUniqueAsync(string transactionNumber, Guid? excludeId = null, CancellationToken cancellationToken = default);
}

public interface IUnitOfWork : IDisposable
{
    IUserRepository Users { get; }
    IRepository<Role> Roles { get; }
    IRepository<Permission> Permissions { get; }
    IPartnerRepository Partners { get; }
    ICapitalTransactionRepository CapitalTransactions { get; }
    IRepository<ProfitDistribution> ProfitDistributions { get; }
    IRepository<Category> Categories { get; }
    IRepository<MeasurementUnit> MeasurementUnits { get; }
    IRepository<StorageLocation> StorageLocations { get; }
    IRawMaterialRepository RawMaterials { get; }
    IFinishedProductRepository FinishedProducts { get; }
    IInventoryMovementRepository InventoryMovements { get; }
    ICustomerRepository Customers { get; }
    ISupplierRepository Suppliers { get; }
    IRepository<Department> Departments { get; }
    IRepository<Employee> Employees { get; }
    IRepository<FinancialAccount> FinancialAccounts { get; }
    IFinancialTransactionRepository FinancialTransactions { get; }
    IRepository<SystemSetting> SystemSettings { get; }
    IRepository<AuditLog> AuditLogs { get; }

    Task<int> SaveChangesAsync(CancellationToken cancellationToken = default);
    Task BeginTransactionAsync(CancellationToken cancellationToken = default);
    Task CommitTransactionAsync(CancellationToken cancellationToken = default);
    Task RollbackTransactionAsync(CancellationToken cancellationToken = default);
}
