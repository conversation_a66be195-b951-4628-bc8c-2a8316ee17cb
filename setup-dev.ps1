# Armored Doors Manufacturing Management System - Development Setup Script
# This script sets up the development environment for the project

Write-Host "Setting up Armored Doors Manufacturing Management System..." -ForegroundColor Green

# Check prerequisites
Write-Host "Checking prerequisites..." -ForegroundColor Yellow

# Check .NET SDK
try {
    $dotnetVersion = dotnet --version
    Write-Host "✓ .NET SDK found: $dotnetVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ .NET SDK not found. Please install .NET 8.0 SDK" -ForegroundColor Red
    exit 1
}

# Check Node.js
try {
    $nodeVersion = node --version
    Write-Host "✓ Node.js found: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "✗ Node.js not found. Please install Node.js 18+" -ForegroundColor Red
    exit 1
}

# Check Docker
try {
    $dockerVersion = docker --version
    Write-Host "✓ Docker found: $dockerVersion" -ForegroundColor Green
} catch {
    Write-Host "⚠ Docker not found. Docker is optional for development but required for production" -ForegroundColor Yellow
}

Write-Host "`nSetting up backend projects..." -ForegroundColor Yellow

# Restore .NET packages
Write-Host "Restoring .NET packages..." -ForegroundColor Cyan
dotnet restore

# Add project references
Write-Host "Adding project references..." -ForegroundColor Cyan

# API references
dotnet add src/Backend/ArmoredDoors.API/ArmoredDoors.API.csproj reference src/Backend/ArmoredDoors.Application/ArmoredDoors.Application.csproj
dotnet add src/Backend/ArmoredDoors.API/ArmoredDoors.API.csproj reference src/Backend/ArmoredDoors.Infrastructure/ArmoredDoors.Infrastructure.csproj
dotnet add src/Backend/ArmoredDoors.API/ArmoredDoors.API.csproj reference src/Backend/ArmoredDoors.Shared/ArmoredDoors.Shared.csproj

# Application references
dotnet add src/Backend/ArmoredDoors.Application/ArmoredDoors.Application.csproj reference src/Backend/ArmoredDoors.Core/ArmoredDoors.Core.csproj
dotnet add src/Backend/ArmoredDoors.Application/ArmoredDoors.Application.csproj reference src/Backend/ArmoredDoors.Shared/ArmoredDoors.Shared.csproj

# Infrastructure references
dotnet add src/Backend/ArmoredDoors.Infrastructure/ArmoredDoors.Infrastructure.csproj reference src/Backend/ArmoredDoors.Core/ArmoredDoors.Core.csproj
dotnet add src/Backend/ArmoredDoors.Infrastructure/ArmoredDoors.Infrastructure.csproj reference src/Backend/ArmoredDoors.Shared/ArmoredDoors.Shared.csproj

# Test project references
dotnet add src/Tests/ArmoredDoors.UnitTests/ArmoredDoors.UnitTests.csproj reference src/Backend/ArmoredDoors.Core/ArmoredDoors.Core.csproj
dotnet add src/Tests/ArmoredDoors.UnitTests/ArmoredDoors.UnitTests.csproj reference src/Backend/ArmoredDoors.Application/ArmoredDoors.Application.csproj

dotnet add src/Tests/ArmoredDoors.IntegrationTests/ArmoredDoors.IntegrationTests.csproj reference src/Backend/ArmoredDoors.API/ArmoredDoors.API.csproj
dotnet add src/Tests/ArmoredDoors.IntegrationTests/ArmoredDoors.IntegrationTests.csproj reference src/Backend/ArmoredDoors.Infrastructure/ArmoredDoors.Infrastructure.csproj

Write-Host "`nSetting up frontend..." -ForegroundColor Yellow

# Install Angular dependencies
Set-Location src/Frontend/armored-doors-app
Write-Host "Installing Angular dependencies..." -ForegroundColor Cyan
npm install

# Install additional packages for the project
Write-Host "Installing additional Angular packages..." -ForegroundColor Cyan
npm install @angular/material @angular/cdk @angular/animations
npm install @ngrx/store @ngrx/effects @ngrx/store-devtools
npm install @microsoft/signalr
npm install tailwindcss @tailwindcss/forms @tailwindcss/typography
npm install chart.js ng2-charts
npm install ngx-barcode-scanner
npm install ngx-print
npm install moment
npm install --save-dev @types/chart.js

Set-Location ../../..

Write-Host "`nCreating development configuration files..." -ForegroundColor Yellow

# Create appsettings.Development.json
$appsettingsContent = @"
{
  "ConnectionStrings": {
    "DefaultConnection": "Server=localhost;Database=ArmoredDoorsDB;Trusted_Connection=true;TrustServerCertificate=true;",
    "Redis": "localhost:6379"
  },
  "JWT": {
    "SecretKey": "ArmoredDoorsSecretKeyForJWTTokenGeneration2024!",
    "Issuer": "ArmoredDoorsAPI",
    "Audience": "ArmoredDoorsApp",
    "ExpiryInMinutes": 60,
    "RefreshTokenExpiryInDays": 7
  },
  "Logging": {
    "LogLevel": {
      "Default": "Information",
      "Microsoft.AspNetCore": "Warning",
      "Microsoft.EntityFrameworkCore": "Information"
    }
  },
  "AllowedHosts": "*",
  "CORS": {
    "AllowedOrigins": ["http://localhost:4200", "https://localhost:4200"]
  },
  "FileStorage": {
    "BasePath": "wwwroot/uploads",
    "MaxFileSizeInMB": 10,
    "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"]
  },
  "SignalR": {
    "EnableDetailedErrors": true
  }
}
"@

$appsettingsContent | Out-File -FilePath "src/Backend/ArmoredDoors.API/appsettings.Development.json" -Encoding UTF8

Write-Host "`n✓ Development environment setup completed!" -ForegroundColor Green
Write-Host "`nNext steps:" -ForegroundColor Yellow
Write-Host "1. Set up SQL Server (local instance or Docker)" -ForegroundColor White
Write-Host "2. Set up Redis (local instance or Docker)" -ForegroundColor White
Write-Host "3. Run database migrations: dotnet ef database update" -ForegroundColor White
Write-Host "4. Start the API: dotnet run --project src/Backend/ArmoredDoors.API" -ForegroundColor White
Write-Host "5. Start the frontend: cd src/Frontend/armored-doors-app && ng serve" -ForegroundColor White
Write-Host "`nOr use Docker: docker-compose up" -ForegroundColor White

Write-Host "`nDevelopment URLs:" -ForegroundColor Yellow
Write-Host "API: https://localhost:5001 or http://localhost:5000" -ForegroundColor White
Write-Host "Frontend: http://localhost:4200" -ForegroundColor White
Write-Host "Swagger: https://localhost:5001/swagger" -ForegroundColor White
