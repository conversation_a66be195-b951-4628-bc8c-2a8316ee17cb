import { Component, Input, Output, EventEmitter, OnInit, OnDestroy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule, Router } from '@angular/router';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { LanguageService } from '../../core/services/language';
import { AuthService } from '../../core/services/auth';

interface MenuItem {
  id: string;
  label: string;
  labelAr: string;
  icon: string;
  route?: string;
  children?: MenuItem[];
  permission?: string;
  badge?: string;
  badgeColor?: string;
}

@Component({
  selector: 'app-sidebar',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './sidebar.html',
  styleUrl: './sidebar.scss'
})
export class SidebarComponent implements OnInit, OnDestroy {
  @Input() isOpen = false;
  @Input() isMobile = false;
  @Input() isTablet = false;
  @Input() isDesktop = false;
  @Output() closeSidebar = new EventEmitter<void>();

  menuItems: MenuItem[] = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      labelAr: 'لوحة التحكم',
      icon: 'dashboard',
      route: '/dashboard'
    },
    {
      id: 'inventory',
      label: 'Inventory Management',
      labelAr: 'إدارة المخزون',
      icon: 'inventory',
      children: [
        {
          id: 'raw-materials',
          label: 'Raw Materials',
          labelAr: 'المواد الخام',
          icon: 'materials',
          route: '/inventory/raw-materials'
        },
        {
          id: 'finished-products',
          label: 'Finished Products',
          labelAr: 'المنتجات النهائية',
          icon: 'products',
          route: '/inventory/finished-products'
        },
        {
          id: 'stock-movements',
          label: 'Stock Movements',
          labelAr: 'حركات المخزون',
          icon: 'movements',
          route: '/inventory/movements'
        }
      ]
    },
    {
      id: 'production',
      label: 'Production',
      labelAr: 'الإنتاج',
      icon: 'production',
      children: [
        {
          id: 'work-orders',
          label: 'Work Orders',
          labelAr: 'أوامر العمل',
          icon: 'orders',
          route: '/production/work-orders'
        },
        {
          id: 'quality-control',
          label: 'Quality Control',
          labelAr: 'مراقبة الجودة',
          icon: 'quality',
          route: '/production/quality-control'
        }
      ]
    },
    {
      id: 'sales',
      label: 'Sales & Customers',
      labelAr: 'المبيعات والعملاء',
      icon: 'sales',
      children: [
        {
          id: 'customers',
          label: 'Customers',
          labelAr: 'العملاء',
          icon: 'customers',
          route: '/sales/customers'
        },
        {
          id: 'orders',
          label: 'Sales Orders',
          labelAr: 'أوامر البيع',
          icon: 'orders',
          route: '/sales/orders'
        },
        {
          id: 'invoices',
          label: 'Invoices',
          labelAr: 'الفواتير',
          icon: 'invoices',
          route: '/sales/invoices'
        }
      ]
    },
    {
      id: 'purchasing',
      label: 'Purchasing',
      labelAr: 'المشتريات',
      icon: 'purchasing',
      children: [
        {
          id: 'suppliers',
          label: 'Suppliers',
          labelAr: 'الموردين',
          icon: 'suppliers',
          route: '/purchasing/suppliers'
        },
        {
          id: 'purchase-orders',
          label: 'Purchase Orders',
          labelAr: 'أوامر الشراء',
          icon: 'orders',
          route: '/purchasing/orders'
        }
      ]
    },
    {
      id: 'finance',
      label: 'Finance',
      labelAr: 'المالية',
      icon: 'finance',
      children: [
        {
          id: 'accounts',
          label: 'Chart of Accounts',
          labelAr: 'دليل الحسابات',
          icon: 'accounts',
          route: '/finance/accounts'
        },
        {
          id: 'transactions',
          label: 'Transactions',
          labelAr: 'المعاملات',
          icon: 'transactions',
          route: '/finance/transactions'
        },
        {
          id: 'reports',
          label: 'Financial Reports',
          labelAr: 'التقارير المالية',
          icon: 'reports',
          route: '/finance/reports'
        }
      ]
    },
    {
      id: 'users',
      label: 'User Management',
      labelAr: 'إدارة المستخدمين',
      icon: 'users',
      route: '/users',
      permission: 'manage_users'
    },
    {
      id: 'settings',
      label: 'Settings',
      labelAr: 'الإعدادات',
      icon: 'settings',
      route: '/settings'
    }
  ];

  expandedItems: Set<string> = new Set();

  private destroy$ = new Subject<void>();

  constructor(
    private languageService: LanguageService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Subscribe to language changes
    this.languageService.currentLanguage$
      .pipe(takeUntil(this.destroy$))
      .subscribe(() => {
        // Language changes are handled in template
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleExpanded(itemId: string): void {
    if (this.expandedItems.has(itemId)) {
      this.expandedItems.delete(itemId);
    } else {
      this.expandedItems.add(itemId);
    }
  }

  isExpanded(itemId: string): boolean {
    return this.expandedItems.has(itemId);
  }

  navigateToRoute(route: string): void {
    this.router.navigate([route]);

    // Close sidebar on mobile/tablet after navigation
    if (this.isMobile || this.isTablet) {
      this.closeSidebar.emit();
    }
  }

  hasPermission(permission?: string): boolean {
    if (!permission) return true;
    return this.authService.hasPermission(permission);
  }

  getMenuItemLabel(item: MenuItem): string {
    return this.isRTL ? item.labelAr : item.label;
  }

  getIconClass(iconName: string): string {
    const iconMap: { [key: string]: string } = {
      dashboard: 'M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586l-2 2V5H5v14h14v-3.586l2-2V19a1 1 0 01-1 1H4a1 1 0 01-1-1V4z',
      inventory: 'M4 6h16v2H4zm0 5h16v2H4zm0 5h16v2H4z',
      production: 'M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z',
      sales: 'M16 6V4a2 2 0 00-2-2h-4a2 2 0 00-2 2v2H4a2 2 0 00-2 2v10a2 2 0 002 2h16a2 2 0 002-2V8a2 2 0 00-2-2h-4z',
      purchasing: 'M3 3h2l.4 2M7 13h10l4-8H5.4m1.6 8L5 3H3m4 10v6a2 2 0 002 2h6a2 2 0 002-2v-6',
      finance: 'M12 2v20M17 5H9.5a3.5 3.5 0 000 7h5a3.5 3.5 0 010 7H6',
      users: 'M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z',
      settings: 'M12 15a3 3 0 100-6 3 3 0 000 6z',
      materials: 'M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10',
      products: 'M5 8h14M5 8a2 2 0 110-4h14a2 2 0 110 4M5 8v10a2 2 0 002 2h10a2 2 0 002-2V8',
      movements: 'M7 16l-4-4m0 0l4-4m-4 4h18',
      orders: 'M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2',
      quality: 'M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z',
      customers: 'M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857',
      invoices: 'M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z',
      suppliers: 'M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1',
      accounts: 'M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z',
      transactions: 'M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4',
      reports: 'M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z'
    };

    return iconMap[iconName] || iconMap['dashboard'];
  }

  get isRTL(): boolean {
    return this.languageService.isRTL();
  }

  get currentLanguage() {
    return this.languageService.getCurrentLanguage();
  }

  get isCollapsed(): boolean {
    return !this.isOpen && this.isDesktop;
  }
}
