using Microsoft.EntityFrameworkCore;
using ArmoredDoors.Core.Entities;
using ArmoredDoors.Core.Interfaces;
using ArmoredDoors.Infrastructure.Data;

namespace ArmoredDoors.Infrastructure.Repositories;

public class CustomerRepository : Repository<Customer>, ICustomerRepository
{
    public CustomerRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<Customer?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(c => c.FinancialTransactions)
            .FirstOrDefaultAsync(c => c.Code == code, cancellationToken);
    }

    public async Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(c => c.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(c => c.Id != excludeId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task<IEnumerable<Customer>> GetByTypeAsync(string customerType, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(c => c.CustomerType == customerType && c.IsActive)
            .OrderBy(c => c.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Customer>> GetWithOutstandingBalanceAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(c => c.IsActive && c.CurrentBalance != 0)
            .OrderByDescending(c => Math.Abs(c.CurrentBalance))
            .ToListAsync(cancellationToken);
    }
}

public class SupplierRepository : Repository<Supplier>, ISupplierRepository
{
    public SupplierRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<Supplier?> GetByCodeAsync(string code, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(s => s.FinancialTransactions)
            .FirstOrDefaultAsync(s => s.Code == code, cancellationToken);
    }

    public async Task<bool> IsCodeUniqueAsync(string code, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(s => s.Code == code);
        
        if (excludeId.HasValue)
        {
            query = query.Where(s => s.Id != excludeId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }

    public async Task<IEnumerable<Supplier>> GetByTypeAsync(string supplierType, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.SupplierType == supplierType && s.IsActive)
            .OrderBy(s => s.Name)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<Supplier>> GetWithOutstandingBalanceAsync(CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Where(s => s.IsActive && s.CurrentBalance != 0)
            .OrderByDescending(s => Math.Abs(s.CurrentBalance))
            .ToListAsync(cancellationToken);
    }
}

public class FinancialTransactionRepository : Repository<FinancialTransaction>, IFinancialTransactionRepository
{
    public FinancialTransactionRepository(ArmoredDoorsDbContext context) : base(context)
    {
    }

    public async Task<FinancialTransaction?> GetByTransactionNumberAsync(string transactionNumber, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(ft => ft.TransactionDetails)
                .ThenInclude(ftd => ftd.Account)
            .Include(ft => ft.ApprovedByUser)
            .Include(ft => ft.CreatedByUser)
            .FirstOrDefaultAsync(ft => ft.TransactionNumber == transactionNumber, cancellationToken);
    }

    public async Task<IEnumerable<FinancialTransaction>> GetByEntityAsync(string entityType, Guid entityId, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(ft => ft.TransactionDetails)
                .ThenInclude(ftd => ftd.Account)
            .Include(ft => ft.ApprovedByUser)
            .Include(ft => ft.CreatedByUser)
            .Where(ft => ft.EntityType == entityType && ft.EntityId == entityId)
            .OrderByDescending(ft => ft.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<FinancialTransaction>> GetByDateRangeAsync(DateTime fromDate, DateTime toDate, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(ft => ft.TransactionDetails)
                .ThenInclude(ftd => ftd.Account)
            .Include(ft => ft.ApprovedByUser)
            .Include(ft => ft.CreatedByUser)
            .Where(ft => ft.TransactionDate >= fromDate && ft.TransactionDate <= toDate)
            .OrderByDescending(ft => ft.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<IEnumerable<FinancialTransaction>> GetByTypeAsync(string transactionType, CancellationToken cancellationToken = default)
    {
        return await _dbSet
            .Include(ft => ft.TransactionDetails)
                .ThenInclude(ftd => ftd.Account)
            .Include(ft => ft.ApprovedByUser)
            .Include(ft => ft.CreatedByUser)
            .Where(ft => ft.TransactionType == transactionType)
            .OrderByDescending(ft => ft.TransactionDate)
            .ToListAsync(cancellationToken);
    }

    public async Task<bool> IsTransactionNumberUniqueAsync(string transactionNumber, Guid? excludeId = null, CancellationToken cancellationToken = default)
    {
        var query = _dbSet.Where(ft => ft.TransactionNumber == transactionNumber);
        
        if (excludeId.HasValue)
        {
            query = query.Where(ft => ft.Id != excludeId.Value);
        }
        
        return !await query.AnyAsync(cancellationToken);
    }
}
