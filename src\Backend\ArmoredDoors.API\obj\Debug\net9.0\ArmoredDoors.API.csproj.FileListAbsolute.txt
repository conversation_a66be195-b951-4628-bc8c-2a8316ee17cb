D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\appsettings.Development.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\appsettings.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.API.staticwebassets.endpoints.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.API.exe
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.API.deps.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.API.runtimeconfig.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.API.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.API.pdb
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\AutoMapper.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Azure.Core.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Azure.Identity.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Humanizer.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.AspNetCore.Authentication.JwtBearer.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.AspNetCore.OpenApi.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Bcl.AsyncInterfaces.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Build.Locator.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.CodeAnalysis.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.CodeAnalysis.CSharp.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.CodeAnalysis.CSharp.Workspaces.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.CodeAnalysis.Workspaces.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.CodeAnalysis.Workspaces.MSBuild.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Data.SqlClient.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Abstractions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Design.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.Relational.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.EntityFrameworkCore.SqlServer.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Caching.Abstractions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Caching.Memory.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Configuration.Abstractions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.DependencyInjection.Abstractions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.DependencyModel.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Logging.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Logging.Abstractions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Options.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Extensions.Primitives.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Identity.Client.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Identity.Client.Extensions.Msal.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.IdentityModel.Abstractions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.IdentityModel.JsonWebTokens.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.IdentityModel.Logging.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.IdentityModel.Tokens.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.OpenApi.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.SqlServer.Server.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Microsoft.Win32.SystemEvents.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\Mono.TextTemplating.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.ClientModel.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.CodeDom.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Composition.AttributedModel.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Composition.Convention.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Composition.Hosting.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Composition.Runtime.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Composition.TypedParts.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Configuration.ConfigurationManager.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Drawing.Common.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Formats.Asn1.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.IdentityModel.Tokens.Jwt.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Memory.Data.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Runtime.Caching.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Security.Cryptography.ProtectedData.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Security.Permissions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Text.Json.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\System.Windows.Extensions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.CSharp.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\cs\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\de\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\es\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\fr\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\it\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ja\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ko\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pl\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\pt-BR\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ru\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\tr\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hans\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\zh-Hant\Microsoft.CodeAnalysis.Workspaces.MSBuild.BuildHost.resources.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\unix\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win\lib\net6.0\Microsoft.Data.SqlClient.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win-arm\native\Microsoft.Data.SqlClient.SNI.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win-arm64\native\Microsoft.Data.SqlClient.SNI.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win-x64\native\Microsoft.Data.SqlClient.SNI.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win-x86\native\Microsoft.Data.SqlClient.SNI.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win\lib\net6.0\Microsoft.Win32.SystemEvents.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\unix\lib\net6.0\System.Drawing.Common.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win\lib\net6.0\System.Drawing.Common.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win\lib\net6.0\System.Runtime.Caching.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win\lib\net6.0\System.Security.Cryptography.ProtectedData.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\runtimes\win\lib\net6.0\System.Windows.Extensions.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.Application.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.Core.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.Infrastructure.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.Application.pdb
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.Infrastructure.pdb
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\bin\Debug\net9.0\ArmoredDoors.Core.pdb
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.csproj.AssemblyReference.cache
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.GeneratedMSBuildEditorConfig.editorconfig
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.AssemblyInfoInputs.cache
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.AssemblyInfo.cs
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.csproj.CoreCompileInputs.cache
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.MvcApplicationPartsAssemblyInfo.cs
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.MvcApplicationPartsAssemblyInfo.cache
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\scopedcss\bundle\ArmoredDoors.API.styles.css
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets.build.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets.development.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets.build.endpoints.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets\msbuild.ArmoredDoors.API.Microsoft.AspNetCore.StaticWebAssets.props
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets\msbuild.ArmoredDoors.API.Microsoft.AspNetCore.StaticWebAssetEndpoints.props
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets\msbuild.build.ArmoredDoors.API.props
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets\msbuild.buildMultiTargeting.ArmoredDoors.API.props
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets\msbuild.buildTransitive.ArmoredDoors.API.props
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\staticwebassets.pack.json
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredD.0F3492F8.Up2Date
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\refint\ArmoredDoors.API.dll
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.pdb
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ArmoredDoors.API.genruntimeconfig.cache
D:\AIProjectTest\Sourcs\FullStackApp\src\Backend\ArmoredDoors.API\obj\Debug\net9.0\ref\ArmoredDoors.API.dll
