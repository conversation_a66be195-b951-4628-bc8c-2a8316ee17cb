<div class="relative inline-block text-left">
  <!-- Language Switcher Button -->
  <button
    type="button"
    class="inline-flex items-center justify-center w-full px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-primary-500 transition-colors duration-200"
    (click)="toggleDropdown()"
    (keydown)="onKeyDown($event)"
    [attr.aria-expanded]="isDropdownOpen"
    aria-haspopup="true"
  >
    <span class="flex items-center">
      <span class="text-lg mr-2">{{ currentLanguage?.flag }}</span>
      <span class="hidden sm:block">{{ currentLanguage?.nativeName }}</span>
      <span class="block sm:hidden">{{ currentLanguage?.code?.toUpperCase() }}</span>
    </span>
    <svg
      class="w-4 h-4 ml-2 -mr-1 transition-transform duration-200"
      [class.rotate-180]="isDropdownOpen"
      xmlns="http://www.w3.org/2000/svg"
      viewBox="0 0 20 20"
      fill="currentColor"
    >
      <path
        fill-rule="evenodd"
        d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
        clip-rule="evenodd"
      />
    </svg>
  </button>

  <!-- Dropdown Menu -->
  <div
    *ngIf="isDropdownOpen"
    class="absolute right-0 z-50 w-48 mt-2 origin-top-right bg-white border border-gray-200 rounded-md shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none animate-fade-in"
    [class.left-0]="currentLanguage?.direction === 'rtl'"
    [class.right-0]="currentLanguage?.direction === 'ltr'"
  >
    <div class="py-1" role="menu" aria-orientation="vertical">
      <button
        *ngFor="let language of supportedLanguages"
        type="button"
        class="flex items-center w-full px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 hover:text-gray-900 transition-colors duration-150"
        [class.bg-gray-100]="language.code === currentLanguage?.code"
        [class.text-gray-900]="language.code === currentLanguage?.code"
        [class.font-medium]="language.code === currentLanguage?.code"
        (click)="selectLanguage(language)"
        role="menuitem"
      >
        <span class="text-lg mr-3">{{ language.flag }}</span>
        <div class="flex flex-col items-start">
          <span class="font-medium">{{ language.nativeName }}</span>
          <span class="text-xs text-gray-500">{{ language.name }}</span>
        </div>
        <svg
          *ngIf="language.code === currentLanguage?.code"
          class="w-4 h-4 ml-auto text-primary-600"
          xmlns="http://www.w3.org/2000/svg"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fill-rule="evenodd"
            d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
            clip-rule="evenodd"
          />
        </svg>
      </button>
    </div>
  </div>

  <!-- Backdrop to close dropdown when clicking outside -->
  <div
    *ngIf="isDropdownOpen"
    class="fixed inset-0 z-40"
    (click)="closeDropdown()"
  ></div>
</div>
