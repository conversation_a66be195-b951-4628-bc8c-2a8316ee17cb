using Microsoft.AspNetCore.Mvc;
using ArmoredDoors.Application.Services;
using Swashbuckle.AspNetCore.Annotations;
using System.ComponentModel.DataAnnotations;

namespace ArmoredDoors.API.Controllers;

/// <summary>
/// Authentication controller for user login, token refresh, and logout operations
/// </summary>
[ApiController]
[Route("api/[controller]")]
[Produces("application/json")]
[SwaggerTag("Authentication operations for user login and token management")]
public class AuthController : ControllerBase
{
    private readonly IAuthenticationService _authenticationService;

    public AuthController(IAuthenticationService authenticationService)
    {
        _authenticationService = authenticationService;
    }

    /// <summary>
    /// Authenticates a user and returns a JWT token with user information and permissions
    /// </summary>
    /// <param name="request">Login credentials containing username and password</param>
    /// <returns>JWT token, refresh token, and user information if authentication is successful</returns>
    /// <response code="200">Authentication successful - returns JWT token and user info</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="401">Invalid username or password</response>
    [HttpPost("login")]
    [SwaggerOperation(
        Summary = "User Login",
        Description = "Authenticates a user with username and password, returning a JWT token and user information including roles and permissions."
    )]
    [SwaggerResponse(200, "Authentication successful", typeof(LoginResponse))]
    [SwaggerResponse(400, "Invalid request data")]
    [SwaggerResponse(401, "Invalid username or password")]
    public async Task<IActionResult> Login([FromBody] LoginRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authenticationService.AuthenticateAsync(request.Username, request.Password);

        if (!result.Success)
        {
            return Unauthorized(new { message = result.Message });
        }

        return Ok(new
        {
            token = result.Token,
            refreshToken = result.RefreshToken,
            user = result.User
        });
    }

    /// <summary>
    /// Refreshes an expired JWT token using a valid refresh token
    /// </summary>
    /// <param name="request">Refresh token request containing the refresh token</param>
    /// <returns>New JWT token and refresh token if the refresh token is valid</returns>
    /// <response code="200">Token refresh successful</response>
    /// <response code="400">Invalid request data</response>
    /// <response code="401">Invalid or expired refresh token</response>
    [HttpPost("refresh")]
    [SwaggerOperation(
        Summary = "Refresh JWT Token",
        Description = "Refreshes an expired JWT token using a valid refresh token. Returns new JWT and refresh tokens."
    )]
    [SwaggerResponse(200, "Token refresh successful", typeof(TokenResponse))]
    [SwaggerResponse(400, "Invalid request data")]
    [SwaggerResponse(401, "Invalid or expired refresh token")]
    public async Task<IActionResult> RefreshToken([FromBody] RefreshTokenRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authenticationService.RefreshTokenAsync(request.RefreshToken);

        if (!result.Success)
        {
            return Unauthorized(new { message = result.Message });
        }

        return Ok(new
        {
            token = result.Token,
            refreshToken = result.RefreshToken
        });
    }

    /// <summary>
    /// Revokes a refresh token to prevent its future use
    /// </summary>
    /// <param name="request">Refresh token request containing the token to revoke</param>
    /// <returns>Success message if the token is revoked successfully</returns>
    /// <response code="200">Token revoked successfully</response>
    /// <response code="400">Invalid request data or failed to revoke token</response>
    [HttpPost("revoke")]
    [SwaggerOperation(
        Summary = "Revoke Refresh Token",
        Description = "Revokes a refresh token to prevent its future use. This is typically used during logout."
    )]
    [SwaggerResponse(200, "Token revoked successfully")]
    [SwaggerResponse(400, "Invalid request data or failed to revoke token")]
    public async Task<IActionResult> RevokeToken([FromBody] RefreshTokenRequest request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        var result = await _authenticationService.RevokeTokenAsync(request.RefreshToken);

        if (result)
        {
            return Ok(new { message = "Token revoked successfully" });
        }

        return BadRequest(new { message = "Failed to revoke token" });
    }
}

/// <summary>
/// Login request model containing user credentials
/// </summary>
public class LoginRequest
{
    /// <summary>
    /// Username for authentication
    /// </summary>
    /// <example>superadmin</example>
    [Required(ErrorMessage = "Username is required")]
    [SwaggerSchema("The username for authentication")]
    public string Username { get; set; } = string.Empty;

    /// <summary>
    /// Password for authentication
    /// </summary>
    /// <example>SuperAdmin123!</example>
    [Required(ErrorMessage = "Password is required")]
    [SwaggerSchema("The password for authentication")]
    public string Password { get; set; } = string.Empty;
}

/// <summary>
/// Refresh token request model
/// </summary>
public class RefreshTokenRequest
{
    /// <summary>
    /// Refresh token to be used for generating new JWT token
    /// </summary>
    [Required(ErrorMessage = "Refresh token is required")]
    [SwaggerSchema("The refresh token for generating new JWT token")]
    public string RefreshToken { get; set; } = string.Empty;
}

/// <summary>
/// Login response model containing authentication tokens and user information
/// </summary>
public class LoginResponse
{
    /// <summary>
    /// JWT access token
    /// </summary>
    [SwaggerSchema("JWT access token for API authentication")]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// Refresh token for obtaining new access tokens
    /// </summary>
    [SwaggerSchema("Refresh token for obtaining new access tokens")]
    public string RefreshToken { get; set; } = string.Empty;

    /// <summary>
    /// User information including roles and permissions
    /// </summary>
    [SwaggerSchema("User information including roles and permissions")]
    public UserInfo User { get; set; } = new();
}

/// <summary>
/// Token response model for refresh operations
/// </summary>
public class TokenResponse
{
    /// <summary>
    /// New JWT access token
    /// </summary>
    [SwaggerSchema("New JWT access token")]
    public string Token { get; set; } = string.Empty;

    /// <summary>
    /// New refresh token
    /// </summary>
    [SwaggerSchema("New refresh token")]
    public string RefreshToken { get; set; } = string.Empty;
}
