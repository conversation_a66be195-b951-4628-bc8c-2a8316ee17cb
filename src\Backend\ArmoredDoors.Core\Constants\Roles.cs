namespace ArmoredDoors.Core.Constants;

public static class Roles
{
    public const string SuperAdmin = "SuperAdmin";
    public const string Admin = "Admin";
    public const string Manager = "Manager";
    public const string Employee = "Employee";
    public const string Partner = "Partner";
    public const string WarehouseManager = "WarehouseManager";
    public const string FinancialManager = "FinancialManager";
    public const string SalesManager = "SalesManager";
    public const string PurchaseManager = "PurchaseManager";
    public const string Accountant = "Accountant";
    public const string WarehouseEmployee = "WarehouseEmployee";
    public const string SalesEmployee = "SalesEmployee";
    public const string Viewer = "Viewer";

    public static List<(string Name, string Description, List<string> Permissions)> GetDefaultRoles()
    {
        return new List<(string, string, List<string>)>
        {
            (SuperAdmin, "Super Administrator with full system access", new List<string>
            {
                // All permissions - SuperAdmin has access to everything
                Permissions.Users.View, Permissions.Users.Create, Permissions.Users.Edit, Permissions.Users.Delete,
                Permissions.Users.ManageRoles, Permissions.Users.ResetPassword,
                Permissions.Roles.View, Permissions.Roles.Create, Permissions.Roles.Edit, Permissions.Roles.Delete,
                Permissions.Roles.ManagePermissions,
                Permissions.Partners.View, Permissions.Partners.Create, Permissions.Partners.Edit, Permissions.Partners.Delete,
                Permissions.Partners.ViewCapital, Permissions.Partners.ManageCapital, Permissions.Partners.ViewProfitDistribution,
                Permissions.Partners.ManageProfitDistribution, Permissions.Partners.ApproveTransactions,
                Permissions.RawMaterials.View, Permissions.RawMaterials.Create, Permissions.RawMaterials.Edit, Permissions.RawMaterials.Delete,
                Permissions.RawMaterials.ViewStock, Permissions.RawMaterials.ManageStock, Permissions.RawMaterials.ViewMovements,
                Permissions.RawMaterials.CreateMovements, Permissions.RawMaterials.ManageImages,
                Permissions.FinishedProducts.View, Permissions.FinishedProducts.Create, Permissions.FinishedProducts.Edit, Permissions.FinishedProducts.Delete,
                Permissions.FinishedProducts.ViewStock, Permissions.FinishedProducts.ManageStock, Permissions.FinishedProducts.ViewMovements,
                Permissions.FinishedProducts.CreateMovements, Permissions.FinishedProducts.ManageImages, Permissions.FinishedProducts.ManageBarcodes,
                Permissions.FinishedProducts.ViewPricing, Permissions.FinishedProducts.ManagePricing,
                Permissions.Warehouse.ViewLocations, Permissions.Warehouse.ManageLocations, Permissions.Warehouse.ViewCategories,
                Permissions.Warehouse.ManageCategories, Permissions.Warehouse.ViewMeasurementUnits, Permissions.Warehouse.ManageMeasurementUnits,
                Permissions.Warehouse.ViewInventoryReports, Permissions.Warehouse.ManageInventoryAdjustments,
                Permissions.Customers.View, Permissions.Customers.Create, Permissions.Customers.Edit, Permissions.Customers.Delete,
                Permissions.Customers.ViewBalance, Permissions.Customers.ManageCredit, Permissions.Customers.ViewTransactions,
                Permissions.Customers.ViewStatements,
                Permissions.Suppliers.View, Permissions.Suppliers.Create, Permissions.Suppliers.Edit, Permissions.Suppliers.Delete,
                Permissions.Suppliers.ViewBalance, Permissions.Suppliers.ViewTransactions, Permissions.Suppliers.ViewStatements,
                Permissions.Financial.ViewTransactions, Permissions.Financial.CreateTransactions, Permissions.Financial.EditTransactions,
                Permissions.Financial.DeleteTransactions, Permissions.Financial.ApproveTransactions, Permissions.Financial.ViewAccounts,
                Permissions.Financial.ManageAccounts, Permissions.Financial.ViewReports, Permissions.Financial.ViewCashFlow,
                Permissions.Financial.ManageCashFlow, Permissions.Financial.ViewProfitLoss, Permissions.Financial.ViewBalanceSheet,
                Permissions.Employees.View, Permissions.Employees.Create, Permissions.Employees.Edit, Permissions.Employees.Delete,
                Permissions.Employees.ViewSalary, Permissions.Employees.ManageSalary, Permissions.Employees.ViewCustody,
                Permissions.Employees.ManageCustody,
                Permissions.Departments.View, Permissions.Departments.Create, Permissions.Departments.Edit, Permissions.Departments.Delete,
                Permissions.Departments.ViewBudget, Permissions.Departments.ManageBudget, Permissions.Departments.ViewExpenses,
                Permissions.Departments.ManageExpenses,
                Permissions.Reports.ViewInventoryReports, Permissions.Reports.ViewFinancialReports, Permissions.Reports.ViewPartnerReports,
                Permissions.Reports.ViewCustomerReports, Permissions.Reports.ViewSupplierReports, Permissions.Reports.ViewEmployeeReports,
                Permissions.Reports.ExportReports, Permissions.Reports.PrintReports, Permissions.Reports.ViewDashboard,
                Permissions.System.ViewSettings, Permissions.System.ManageSettings, Permissions.System.ViewAuditLogs,
                Permissions.System.ManageBackups, Permissions.System.ViewSystemInfo, Permissions.System.ManageNotifications
            }),

            (Admin, "Administrator with broad system access", new List<string>
            {
                Permissions.Users.View, Permissions.Users.Create, Permissions.Users.Edit, Permissions.Users.ManageRoles,
                Permissions.Roles.View,
                Permissions.Partners.View, Permissions.Partners.Create, Permissions.Partners.Edit,
                Permissions.Partners.ViewCapital, Permissions.Partners.ViewProfitDistribution,
                Permissions.RawMaterials.View, Permissions.RawMaterials.Create, Permissions.RawMaterials.Edit,
                Permissions.RawMaterials.ViewStock, Permissions.RawMaterials.ManageStock, Permissions.RawMaterials.ViewMovements,
                Permissions.RawMaterials.CreateMovements, Permissions.RawMaterials.ManageImages,
                Permissions.FinishedProducts.View, Permissions.FinishedProducts.Create, Permissions.FinishedProducts.Edit,
                Permissions.FinishedProducts.ViewStock, Permissions.FinishedProducts.ManageStock, Permissions.FinishedProducts.ViewMovements,
                Permissions.FinishedProducts.CreateMovements, Permissions.FinishedProducts.ManageImages, Permissions.FinishedProducts.ManageBarcodes,
                Permissions.FinishedProducts.ViewPricing, Permissions.FinishedProducts.ManagePricing,
                Permissions.Warehouse.ViewLocations, Permissions.Warehouse.ManageLocations, Permissions.Warehouse.ViewCategories,
                Permissions.Warehouse.ManageCategories, Permissions.Warehouse.ViewMeasurementUnits, Permissions.Warehouse.ManageMeasurementUnits,
                Permissions.Warehouse.ViewInventoryReports, Permissions.Warehouse.ManageInventoryAdjustments,
                Permissions.Customers.View, Permissions.Customers.Create, Permissions.Customers.Edit,
                Permissions.Customers.ViewBalance, Permissions.Customers.ManageCredit, Permissions.Customers.ViewTransactions,
                Permissions.Customers.ViewStatements,
                Permissions.Suppliers.View, Permissions.Suppliers.Create, Permissions.Suppliers.Edit,
                Permissions.Suppliers.ViewBalance, Permissions.Suppliers.ViewTransactions, Permissions.Suppliers.ViewStatements,
                Permissions.Financial.ViewTransactions, Permissions.Financial.CreateTransactions, Permissions.Financial.EditTransactions,
                Permissions.Financial.ViewAccounts, Permissions.Financial.ViewReports, Permissions.Financial.ViewCashFlow,
                Permissions.Financial.ViewProfitLoss, Permissions.Financial.ViewBalanceSheet,
                Permissions.Employees.View, Permissions.Employees.Create, Permissions.Employees.Edit,
                Permissions.Employees.ViewSalary, Permissions.Employees.ViewCustody,
                Permissions.Departments.View, Permissions.Departments.Create, Permissions.Departments.Edit,
                Permissions.Departments.ViewBudget, Permissions.Departments.ViewExpenses,
                Permissions.Reports.ViewInventoryReports, Permissions.Reports.ViewFinancialReports, Permissions.Reports.ViewPartnerReports,
                Permissions.Reports.ViewCustomerReports, Permissions.Reports.ViewSupplierReports, Permissions.Reports.ViewEmployeeReports,
                Permissions.Reports.ExportReports, Permissions.Reports.PrintReports, Permissions.Reports.ViewDashboard,
                Permissions.System.ViewSettings, Permissions.System.ViewAuditLogs, Permissions.System.ViewSystemInfo
            }),

            (Manager, "Manager with departmental oversight", new List<string>
            {
                Permissions.Users.View,
                Permissions.Partners.View, Permissions.Partners.ViewCapital, Permissions.Partners.ViewProfitDistribution,
                Permissions.RawMaterials.View, Permissions.RawMaterials.ViewStock, Permissions.RawMaterials.ViewMovements,
                Permissions.FinishedProducts.View, Permissions.FinishedProducts.ViewStock, Permissions.FinishedProducts.ViewMovements,
                Permissions.FinishedProducts.ViewPricing,
                Permissions.Warehouse.ViewLocations, Permissions.Warehouse.ViewCategories, Permissions.Warehouse.ViewMeasurementUnits,
                Permissions.Warehouse.ViewInventoryReports,
                Permissions.Customers.View, Permissions.Customers.ViewBalance, Permissions.Customers.ViewTransactions,
                Permissions.Customers.ViewStatements,
                Permissions.Suppliers.View, Permissions.Suppliers.ViewBalance, Permissions.Suppliers.ViewTransactions,
                Permissions.Suppliers.ViewStatements,
                Permissions.Financial.ViewTransactions, Permissions.Financial.ViewAccounts, Permissions.Financial.ViewReports,
                Permissions.Financial.ViewCashFlow, Permissions.Financial.ViewProfitLoss, Permissions.Financial.ViewBalanceSheet,
                Permissions.Employees.View, Permissions.Employees.ViewSalary, Permissions.Employees.ViewCustody,
                Permissions.Departments.View, Permissions.Departments.ViewBudget, Permissions.Departments.ViewExpenses,
                Permissions.Reports.ViewInventoryReports, Permissions.Reports.ViewFinancialReports, Permissions.Reports.ViewPartnerReports,
                Permissions.Reports.ViewCustomerReports, Permissions.Reports.ViewSupplierReports, Permissions.Reports.ViewEmployeeReports,
                Permissions.Reports.ExportReports, Permissions.Reports.PrintReports, Permissions.Reports.ViewDashboard
            }),

            (WarehouseManager, "Warehouse Manager with inventory control", new List<string>
            {
                Permissions.RawMaterials.View, Permissions.RawMaterials.Create, Permissions.RawMaterials.Edit,
                Permissions.RawMaterials.ViewStock, Permissions.RawMaterials.ManageStock, Permissions.RawMaterials.ViewMovements,
                Permissions.RawMaterials.CreateMovements, Permissions.RawMaterials.ManageImages,
                Permissions.FinishedProducts.View, Permissions.FinishedProducts.Create, Permissions.FinishedProducts.Edit,
                Permissions.FinishedProducts.ViewStock, Permissions.FinishedProducts.ManageStock, Permissions.FinishedProducts.ViewMovements,
                Permissions.FinishedProducts.CreateMovements, Permissions.FinishedProducts.ManageImages, Permissions.FinishedProducts.ManageBarcodes,
                Permissions.Warehouse.ViewLocations, Permissions.Warehouse.ManageLocations, Permissions.Warehouse.ViewCategories,
                Permissions.Warehouse.ManageCategories, Permissions.Warehouse.ViewMeasurementUnits, Permissions.Warehouse.ManageMeasurementUnits,
                Permissions.Warehouse.ViewInventoryReports, Permissions.Warehouse.ManageInventoryAdjustments,
                Permissions.Reports.ViewInventoryReports, Permissions.Reports.ExportReports, Permissions.Reports.PrintReports
            }),

            (FinancialManager, "Financial Manager with accounting oversight", new List<string>
            {
                Permissions.Partners.View, Permissions.Partners.ViewCapital, Permissions.Partners.ViewProfitDistribution,
                Permissions.Customers.View, Permissions.Customers.Create, Permissions.Customers.Edit,
                Permissions.Customers.ViewBalance, Permissions.Customers.ManageCredit, Permissions.Customers.ViewTransactions,
                Permissions.Customers.ViewStatements,
                Permissions.Suppliers.View, Permissions.Suppliers.Create, Permissions.Suppliers.Edit,
                Permissions.Suppliers.ViewBalance, Permissions.Suppliers.ViewTransactions, Permissions.Suppliers.ViewStatements,
                Permissions.Financial.ViewTransactions, Permissions.Financial.CreateTransactions, Permissions.Financial.EditTransactions,
                Permissions.Financial.ViewAccounts, Permissions.Financial.ManageAccounts, Permissions.Financial.ViewReports,
                Permissions.Financial.ViewCashFlow, Permissions.Financial.ManageCashFlow, Permissions.Financial.ViewProfitLoss,
                Permissions.Financial.ViewBalanceSheet,
                Permissions.Employees.View, Permissions.Employees.ViewSalary, Permissions.Employees.ViewCustody,
                Permissions.Departments.View, Permissions.Departments.ViewBudget, Permissions.Departments.ViewExpenses,
                Permissions.Reports.ViewFinancialReports, Permissions.Reports.ViewPartnerReports, Permissions.Reports.ViewCustomerReports,
                Permissions.Reports.ViewSupplierReports, Permissions.Reports.ViewEmployeeReports, Permissions.Reports.ExportReports,
                Permissions.Reports.PrintReports, Permissions.Reports.ViewDashboard
            }),

            (Partner, "Partner with capital and profit visibility", new List<string>
            {
                Permissions.Partners.View, Permissions.Partners.ViewCapital, Permissions.Partners.ViewProfitDistribution,
                Permissions.Financial.ViewReports, Permissions.Financial.ViewProfitLoss, Permissions.Financial.ViewBalanceSheet,
                Permissions.Reports.ViewFinancialReports, Permissions.Reports.ViewPartnerReports, Permissions.Reports.ViewDashboard
            }),

            (Employee, "General Employee with basic access", new List<string>
            {
                Permissions.RawMaterials.View, Permissions.RawMaterials.ViewStock,
                Permissions.FinishedProducts.View, Permissions.FinishedProducts.ViewStock,
                Permissions.Customers.View, Permissions.Suppliers.View,
                Permissions.Reports.ViewDashboard
            }),

            (Viewer, "Read-only access to basic information", new List<string>
            {
                Permissions.RawMaterials.View, Permissions.FinishedProducts.View,
                Permissions.Customers.View, Permissions.Suppliers.View,
                Permissions.Reports.ViewDashboard
            })
        };
    }
}
