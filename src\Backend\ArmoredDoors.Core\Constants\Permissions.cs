namespace ArmoredDoors.Core.Constants;

public static class Permissions
{
    // User Management
    public static class Users
    {
        public const string View = "Users.View";
        public const string Create = "Users.Create";
        public const string Edit = "Users.Edit";
        public const string Delete = "Users.Delete";
        public const string ManageRoles = "Users.ManageRoles";
        public const string ResetPassword = "Users.ResetPassword";
    }

    // Role Management
    public static class Roles
    {
        public const string View = "Roles.View";
        public const string Create = "Roles.Create";
        public const string Edit = "Roles.Edit";
        public const string Delete = "Roles.Delete";
        public const string ManagePermissions = "Roles.ManagePermissions";
    }

    // Partner Management
    public static class Partners
    {
        public const string View = "Partners.View";
        public const string Create = "Partners.Create";
        public const string Edit = "Partners.Edit";
        public const string Delete = "Partners.Delete";
        public const string ViewCapital = "Partners.ViewCapital";
        public const string ManageCapital = "Partners.ManageCapital";
        public const string ViewProfitDistribution = "Partners.ViewProfitDistribution";
        public const string ManageProfitDistribution = "Partners.ManageProfitDistribution";
        public const string ApproveTransactions = "Partners.ApproveTransactions";
    }

    // Raw Materials Management
    public static class RawMaterials
    {
        public const string View = "RawMaterials.View";
        public const string Create = "RawMaterials.Create";
        public const string Edit = "RawMaterials.Edit";
        public const string Delete = "RawMaterials.Delete";
        public const string ViewStock = "RawMaterials.ViewStock";
        public const string ManageStock = "RawMaterials.ManageStock";
        public const string ViewMovements = "RawMaterials.ViewMovements";
        public const string CreateMovements = "RawMaterials.CreateMovements";
        public const string ManageImages = "RawMaterials.ManageImages";
    }

    // Finished Products Management
    public static class FinishedProducts
    {
        public const string View = "FinishedProducts.View";
        public const string Create = "FinishedProducts.Create";
        public const string Edit = "FinishedProducts.Edit";
        public const string Delete = "FinishedProducts.Delete";
        public const string ViewStock = "FinishedProducts.ViewStock";
        public const string ManageStock = "FinishedProducts.ManageStock";
        public const string ViewMovements = "FinishedProducts.ViewMovements";
        public const string CreateMovements = "FinishedProducts.CreateMovements";
        public const string ManageImages = "FinishedProducts.ManageImages";
        public const string ManageBarcodes = "FinishedProducts.ManageBarcodes";
        public const string ViewPricing = "FinishedProducts.ViewPricing";
        public const string ManagePricing = "FinishedProducts.ManagePricing";
    }

    // Warehouse Management
    public static class Warehouse
    {
        public const string ViewLocations = "Warehouse.ViewLocations";
        public const string ManageLocations = "Warehouse.ManageLocations";
        public const string ViewCategories = "Warehouse.ViewCategories";
        public const string ManageCategories = "Warehouse.ManageCategories";
        public const string ViewMeasurementUnits = "Warehouse.ViewMeasurementUnits";
        public const string ManageMeasurementUnits = "Warehouse.ManageMeasurementUnits";
        public const string ViewInventoryReports = "Warehouse.ViewInventoryReports";
        public const string ManageInventoryAdjustments = "Warehouse.ManageInventoryAdjustments";
    }

    // Customer Management
    public static class Customers
    {
        public const string View = "Customers.View";
        public const string Create = "Customers.Create";
        public const string Edit = "Customers.Edit";
        public const string Delete = "Customers.Delete";
        public const string ViewBalance = "Customers.ViewBalance";
        public const string ManageCredit = "Customers.ManageCredit";
        public const string ViewTransactions = "Customers.ViewTransactions";
        public const string ViewStatements = "Customers.ViewStatements";
    }

    // Supplier Management
    public static class Suppliers
    {
        public const string View = "Suppliers.View";
        public const string Create = "Suppliers.Create";
        public const string Edit = "Suppliers.Edit";
        public const string Delete = "Suppliers.Delete";
        public const string ViewBalance = "Suppliers.ViewBalance";
        public const string ViewTransactions = "Suppliers.ViewTransactions";
        public const string ViewStatements = "Suppliers.ViewStatements";
    }

    // Financial Management
    public static class Financial
    {
        public const string ViewTransactions = "Financial.ViewTransactions";
        public const string CreateTransactions = "Financial.CreateTransactions";
        public const string EditTransactions = "Financial.EditTransactions";
        public const string DeleteTransactions = "Financial.DeleteTransactions";
        public const string ApproveTransactions = "Financial.ApproveTransactions";
        public const string ViewAccounts = "Financial.ViewAccounts";
        public const string ManageAccounts = "Financial.ManageAccounts";
        public const string ViewReports = "Financial.ViewReports";
        public const string ViewCashFlow = "Financial.ViewCashFlow";
        public const string ManageCashFlow = "Financial.ManageCashFlow";
        public const string ViewProfitLoss = "Financial.ViewProfitLoss";
        public const string ViewBalanceSheet = "Financial.ViewBalanceSheet";
    }

    // Employee Management
    public static class Employees
    {
        public const string View = "Employees.View";
        public const string Create = "Employees.Create";
        public const string Edit = "Employees.Edit";
        public const string Delete = "Employees.Delete";
        public const string ViewSalary = "Employees.ViewSalary";
        public const string ManageSalary = "Employees.ManageSalary";
        public const string ViewCustody = "Employees.ViewCustody";
        public const string ManageCustody = "Employees.ManageCustody";
    }

    // Department Management
    public static class Departments
    {
        public const string View = "Departments.View";
        public const string Create = "Departments.Create";
        public const string Edit = "Departments.Edit";
        public const string Delete = "Departments.Delete";
        public const string ViewBudget = "Departments.ViewBudget";
        public const string ManageBudget = "Departments.ManageBudget";
        public const string ViewExpenses = "Departments.ViewExpenses";
        public const string ManageExpenses = "Departments.ManageExpenses";
    }

    // Reports
    public static class Reports
    {
        public const string ViewInventoryReports = "Reports.ViewInventoryReports";
        public const string ViewFinancialReports = "Reports.ViewFinancialReports";
        public const string ViewPartnerReports = "Reports.ViewPartnerReports";
        public const string ViewCustomerReports = "Reports.ViewCustomerReports";
        public const string ViewSupplierReports = "Reports.ViewSupplierReports";
        public const string ViewEmployeeReports = "Reports.ViewEmployeeReports";
        public const string ExportReports = "Reports.ExportReports";
        public const string PrintReports = "Reports.PrintReports";
        public const string ViewDashboard = "Reports.ViewDashboard";
    }

    // System Administration
    public static class System
    {
        public const string ViewSettings = "System.ViewSettings";
        public const string ManageSettings = "System.ManageSettings";
        public const string ViewAuditLogs = "System.ViewAuditLogs";
        public const string ManageBackups = "System.ManageBackups";
        public const string ViewSystemInfo = "System.ViewSystemInfo";
        public const string ManageNotifications = "System.ManageNotifications";
    }

    // Get all permissions as a list
    public static List<(string Name, string Module, string Action, string Description)> GetAllPermissions()
    {
        return new List<(string, string, string, string)>
        {
            // User Management
            (Users.View, "Users", "View", "View users list and details"),
            (Users.Create, "Users", "Create", "Create new users"),
            (Users.Edit, "Users", "Edit", "Edit user information"),
            (Users.Delete, "Users", "Delete", "Delete users"),
            (Users.ManageRoles, "Users", "ManageRoles", "Assign and remove user roles"),
            (Users.ResetPassword, "Users", "ResetPassword", "Reset user passwords"),

            // Role Management
            (Roles.View, "Roles", "View", "View roles list and details"),
            (Roles.Create, "Roles", "Create", "Create new roles"),
            (Roles.Edit, "Roles", "Edit", "Edit role information"),
            (Roles.Delete, "Roles", "Delete", "Delete roles"),
            (Roles.ManagePermissions, "Roles", "ManagePermissions", "Assign and remove role permissions"),

            // Partner Management
            (Partners.View, "Partners", "View", "View partners list and details"),
            (Partners.Create, "Partners", "Create", "Create new partners"),
            (Partners.Edit, "Partners", "Edit", "Edit partner information"),
            (Partners.Delete, "Partners", "Delete", "Delete partners"),
            (Partners.ViewCapital, "Partners", "ViewCapital", "View partner capital information"),
            (Partners.ManageCapital, "Partners", "ManageCapital", "Manage partner capital transactions"),
            (Partners.ViewProfitDistribution, "Partners", "ViewProfitDistribution", "View profit distribution"),
            (Partners.ManageProfitDistribution, "Partners", "ManageProfitDistribution", "Manage profit distribution"),
            (Partners.ApproveTransactions, "Partners", "ApproveTransactions", "Approve partner transactions"),

            // Raw Materials
            (RawMaterials.View, "RawMaterials", "View", "View raw materials list and details"),
            (RawMaterials.Create, "RawMaterials", "Create", "Create new raw materials"),
            (RawMaterials.Edit, "RawMaterials", "Edit", "Edit raw material information"),
            (RawMaterials.Delete, "RawMaterials", "Delete", "Delete raw materials"),
            (RawMaterials.ViewStock, "RawMaterials", "ViewStock", "View raw material stock levels"),
            (RawMaterials.ManageStock, "RawMaterials", "ManageStock", "Manage raw material stock"),
            (RawMaterials.ViewMovements, "RawMaterials", "ViewMovements", "View inventory movements"),
            (RawMaterials.CreateMovements, "RawMaterials", "CreateMovements", "Create inventory movements"),
            (RawMaterials.ManageImages, "RawMaterials", "ManageImages", "Manage raw material images"),

            // Finished Products
            (FinishedProducts.View, "FinishedProducts", "View", "View finished products list and details"),
            (FinishedProducts.Create, "FinishedProducts", "Create", "Create new finished products"),
            (FinishedProducts.Edit, "FinishedProducts", "Edit", "Edit finished product information"),
            (FinishedProducts.Delete, "FinishedProducts", "Delete", "Delete finished products"),
            (FinishedProducts.ViewStock, "FinishedProducts", "ViewStock", "View finished product stock levels"),
            (FinishedProducts.ManageStock, "FinishedProducts", "ManageStock", "Manage finished product stock"),
            (FinishedProducts.ViewMovements, "FinishedProducts", "ViewMovements", "View inventory movements"),
            (FinishedProducts.CreateMovements, "FinishedProducts", "CreateMovements", "Create inventory movements"),
            (FinishedProducts.ManageImages, "FinishedProducts", "ManageImages", "Manage product images"),
            (FinishedProducts.ManageBarcodes, "FinishedProducts", "ManageBarcodes", "Manage product barcodes"),
            (FinishedProducts.ViewPricing, "FinishedProducts", "ViewPricing", "View product pricing"),
            (FinishedProducts.ManagePricing, "FinishedProducts", "ManagePricing", "Manage product pricing"),

            // Continue with other modules...
            // (Additional permissions would be added here following the same pattern)
        };
    }
}
