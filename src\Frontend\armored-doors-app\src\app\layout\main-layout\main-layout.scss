// Main Layout Styles
.app-layout {
  @apply min-h-screen bg-gray-50 flex;

  // RTL Support
  &.rtl {
    direction: rtl;
  }
}

// Sidebar Container
.sidebar-container {
  @apply fixed inset-y-0 left-0 z-50 w-64 transform transition-transform duration-300 ease-in-out;

  // RTL positioning
  .rtl & {
    @apply left-auto right-0;
  }

  // Mobile and Tablet: Slide in/out
  @media (max-width: 1023px) {
    &.sidebar-closed {
      @apply -translate-x-full;

      .rtl & {
        @apply translate-x-full;
      }
    }

    &.sidebar-open {
      @apply translate-x-0;
    }
  }

  // Desktop: Always visible, just width changes
  @media (min-width: 1024px) {
    @apply relative transform-none;

    &.sidebar-closed {
      @apply w-16;
    }

    &.sidebar-open {
      @apply w-64;
    }
  }
}

// Main Content Area
.main-content {
  @apply flex-1 flex flex-col min-h-screen transition-all duration-300 ease-in-out;

  // Mobile and Tablet: Full width
  @media (max-width: 1023px) {
    @apply w-full;
  }

  // Desktop: Adjust margin based on sidebar state
  @media (min-width: 1024px) {
    &.sidebar-open {
      @apply ml-64;

      .rtl & {
        @apply ml-0 mr-64;
      }
    }

    &.sidebar-closed {
      @apply ml-16;

      .rtl & {
        @apply ml-0 mr-16;
      }
    }
  }
}

// Page Content
.page-content {
  @apply flex-1 overflow-auto;

  // Responsive padding
  .content-wrapper {
    @apply p-4;

    // Mobile: Smaller padding
    @media (max-width: 767px) {
      @apply p-3;
    }

    // Tablet: Medium padding
    @media (min-width: 768px) and (max-width: 1023px) {
      @apply p-5;
    }

    // Desktop: Larger padding
    @media (min-width: 1024px) {
      @apply p-6;
    }
  }
}

// Responsive Utilities
@media (max-width: 767px) {
  // Mobile specific styles
  .app-layout {
    @apply text-sm;
  }
}

@media (min-width: 768px) and (max-width: 1023px) {
  // Tablet specific styles
  .app-layout {
    @apply text-base;
  }
}

@media (min-width: 1024px) {
  // Desktop specific styles
  .app-layout {
    @apply text-base;
  }
}

// Animation classes
.slide-enter {
  @apply transform -translate-x-full opacity-0;

  .rtl & {
    @apply translate-x-full;
  }
}

.slide-enter-active {
  @apply transition-all duration-300 ease-out;
}

.slide-enter-to {
  @apply transform translate-x-0 opacity-100;
}

.slide-leave {
  @apply transform translate-x-0 opacity-100;
}

.slide-leave-active {
  @apply transition-all duration-300 ease-in;
}

.slide-leave-to {
  @apply transform -translate-x-full opacity-0;

  .rtl & {
    @apply translate-x-full;
  }
}

// Focus management for accessibility
.app-layout:focus-within {
  .sidebar-container {
    @apply ring-2 ring-primary-500 ring-opacity-50;
  }
}

// High contrast mode support
@media (prefers-contrast: high) {
  .app-layout {
    @apply border border-gray-900;
  }

  .sidebar-container {
    @apply border-r border-gray-900;

    .rtl & {
      @apply border-r-0 border-l border-gray-900;
    }
  }
}

// Reduced motion support
@media (prefers-reduced-motion: reduce) {
  .sidebar-container,
  .main-content,
  .slide-enter-active,
  .slide-leave-active {
    @apply transition-none;
  }
}