{"ConnectionStrings": {"DefaultConnection": "Data Source=DESKTOP-G1KQMAJ\\SQLEXPRESS;Initial Catalog=ArmoredDoorsDB;User ID=***;Password=***;TrustServerCertificate=True;MultipleActiveResultSets=True;Integrated Security=False", "Redis": "localhost:6379"}, "JWT": {"SecretKey": "ArmoredDoorsSecretKeyForJWTTokenGeneration2024!", "Issuer": "ArmoredDoorsAPI", "Audience": "ArmoredDoorsApp", "ExpiryInMinutes": 60, "RefreshTokenExpiryInDays": 7}, "Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning", "Microsoft.EntityFrameworkCore": "Information"}}, "AllowedHosts": "*", "CORS": {"AllowedOrigins": ["http://localhost:4200", "https://localhost:4200"]}, "FileStorage": {"BasePath": "wwwroot/uploads", "MaxFileSizeInMB": 10, "AllowedExtensions": [".jpg", ".jpeg", ".png", ".pdf", ".doc", ".docx"]}, "SignalR": {"EnableDetailedErrors": true}}