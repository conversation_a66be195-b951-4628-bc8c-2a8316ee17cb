using Microsoft.EntityFrameworkCore;
using ArmoredDoors.Core.Entities;

namespace ArmoredDoors.Infrastructure.Data;

public class ArmoredDoorsDbContext : DbContext
{
    public ArmoredDoorsDbContext(DbContextOptions<ArmoredDoorsDbContext> options) : base(options)
    {
    }

    // User Management
    public DbSet<User> Users { get; set; }
    public DbSet<Role> Roles { get; set; }
    public DbSet<UserRole> UserRoles { get; set; }
    public DbSet<Permission> Permissions { get; set; }
    public DbSet<RolePermission> RolePermissions { get; set; }

    // Partner Management
    public DbSet<Partner> Partners { get; set; }
    public DbSet<CapitalTransaction> CapitalTransactions { get; set; }
    public DbSet<ProfitDistribution> ProfitDistributions { get; set; }

    // Warehouse Management
    public DbSet<Category> Categories { get; set; }
    public DbSet<MeasurementUnit> MeasurementUnits { get; set; }
    public DbSet<StorageLocation> StorageLocations { get; set; }
    public DbSet<RawMaterial> RawMaterials { get; set; }
    public DbSet<RawMaterialImage> RawMaterialImages { get; set; }
    public DbSet<FinishedProduct> FinishedProducts { get; set; }
    public DbSet<FinishedProductImage> FinishedProductImages { get; set; }
    public DbSet<InventoryMovement> InventoryMovements { get; set; }

    // Financial Management
    public DbSet<Customer> Customers { get; set; }
    public DbSet<Supplier> Suppliers { get; set; }
    public DbSet<Department> Departments { get; set; }
    public DbSet<Employee> Employees { get; set; }
    public DbSet<FinancialAccount> FinancialAccounts { get; set; }
    public DbSet<FinancialTransaction> FinancialTransactions { get; set; }
    public DbSet<FinancialTransactionDetail> FinancialTransactionDetails { get; set; }

    // System
    public DbSet<SystemSetting> SystemSettings { get; set; }
    public DbSet<AuditLog> AuditLogs { get; set; }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        base.OnModelCreating(modelBuilder);

        // Apply all configurations from the current assembly
        modelBuilder.ApplyConfigurationsFromAssembly(typeof(ArmoredDoorsDbContext).Assembly);

        // Configure navigation properties that need explicit configuration
        ConfigureNavigationProperties(modelBuilder);

        // Configure decimal precision globally
        foreach (var entityType in modelBuilder.Model.GetEntityTypes())
        {
            foreach (var property in entityType.GetProperties())
            {
                if (property.ClrType == typeof(decimal) || property.ClrType == typeof(decimal?))
                {
                    property.SetColumnType("decimal(18,2)");
                }
            }
        }

        // Configure unique constraints
        ConfigureUniqueConstraints(modelBuilder);

        // Configure indexes
        ConfigureIndexes(modelBuilder);

        // Configure check constraints
        ConfigureCheckConstraints(modelBuilder);
    }

    private static void ConfigureNavigationProperties(ModelBuilder modelBuilder)
    {
        // Configure InventoryMovement relationships
        modelBuilder.Entity<InventoryMovement>()
            .HasOne(im => im.FromLocation)
            .WithMany(sl => sl.FromMovements)
            .HasForeignKey(im => im.FromLocationId)
            .OnDelete(DeleteBehavior.Restrict);

        modelBuilder.Entity<InventoryMovement>()
            .HasOne(im => im.ToLocation)
            .WithMany(sl => sl.ToMovements)
            .HasForeignKey(im => im.ToLocationId)
            .OnDelete(DeleteBehavior.Restrict);

        // Configure polymorphic relationships for InventoryMovement
        modelBuilder.Entity<InventoryMovement>()
            .Ignore(im => im.RawMaterial)
            .Ignore(im => im.FinishedProduct);

        // Configure FinancialTransaction polymorphic relationships
        modelBuilder.Entity<FinancialTransaction>()
            .Ignore(ft => ft.Customer)
            .Ignore(ft => ft.Supplier)
            .Ignore(ft => ft.Partner)
            .Ignore(ft => ft.Employee)
            .Ignore(ft => ft.Department);
    }

    private static void ConfigureUniqueConstraints(ModelBuilder modelBuilder)
    {
        // User unique constraints
        modelBuilder.Entity<User>()
            .HasIndex(u => u.Username)
            .IsUnique();

        modelBuilder.Entity<User>()
            .HasIndex(u => u.Email)
            .IsUnique();

        // Role unique constraints
        modelBuilder.Entity<Role>()
            .HasIndex(r => r.Name)
            .IsUnique();

        // Permission unique constraints
        modelBuilder.Entity<Permission>()
            .HasIndex(p => p.Name)
            .IsUnique();

        // UserRole unique constraints
        modelBuilder.Entity<UserRole>()
            .HasIndex(ur => new { ur.UserId, ur.RoleId })
            .IsUnique();

        // RolePermission unique constraints
        modelBuilder.Entity<RolePermission>()
            .HasIndex(rp => new { rp.RoleId, rp.PermissionId })
            .IsUnique();

        // Raw Material unique constraints
        modelBuilder.Entity<RawMaterial>()
            .HasIndex(rm => rm.Code)
            .IsUnique();

        // Finished Product unique constraints
        modelBuilder.Entity<FinishedProduct>()
            .HasIndex(fp => fp.Code)
            .IsUnique();

        modelBuilder.Entity<FinishedProduct>()
            .HasIndex(fp => fp.Barcode)
            .IsUnique()
            .HasFilter("[Barcode] IS NOT NULL");

        // Storage Location unique constraints
        modelBuilder.Entity<StorageLocation>()
            .HasIndex(sl => sl.Code)
            .IsUnique();

        // Customer unique constraints
        modelBuilder.Entity<Customer>()
            .HasIndex(c => c.Code)
            .IsUnique();

        // Supplier unique constraints
        modelBuilder.Entity<Supplier>()
            .HasIndex(s => s.Code)
            .IsUnique();

        // Department unique constraints
        modelBuilder.Entity<Department>()
            .HasIndex(d => d.Code)
            .IsUnique();

        // Employee unique constraints
        modelBuilder.Entity<Employee>()
            .HasIndex(e => e.EmployeeNumber)
            .IsUnique();

        // Financial Account unique constraints
        modelBuilder.Entity<FinancialAccount>()
            .HasIndex(fa => fa.AccountCode)
            .IsUnique();

        // Financial Transaction unique constraints
        modelBuilder.Entity<FinancialTransaction>()
            .HasIndex(ft => ft.TransactionNumber)
            .IsUnique();

        // System Setting unique constraints
        modelBuilder.Entity<SystemSetting>()
            .HasIndex(ss => ss.SettingKey)
            .IsUnique();

        // Measurement Unit unique constraints
        modelBuilder.Entity<MeasurementUnit>()
            .HasIndex(mu => mu.Name)
            .IsUnique();
    }

    private static void ConfigureIndexes(ModelBuilder modelBuilder)
    {
        // User indexes
        modelBuilder.Entity<User>()
            .HasIndex(u => u.IsActive);

        // Partner indexes
        modelBuilder.Entity<Partner>()
            .HasIndex(p => p.Name);

        modelBuilder.Entity<Partner>()
            .HasIndex(p => p.IsActive);

        // Capital Transaction indexes
        modelBuilder.Entity<CapitalTransaction>()
            .HasIndex(ct => new { ct.PartnerId, ct.TransactionDate });

        modelBuilder.Entity<CapitalTransaction>()
            .HasIndex(ct => ct.TransactionType);

        // Raw Material indexes
        modelBuilder.Entity<RawMaterial>()
            .HasIndex(rm => rm.Name);

        modelBuilder.Entity<RawMaterial>()
            .HasIndex(rm => rm.CategoryId);

        modelBuilder.Entity<RawMaterial>()
            .HasIndex(rm => rm.IsActive);

        // Finished Product indexes
        modelBuilder.Entity<FinishedProduct>()
            .HasIndex(fp => fp.Name);

        modelBuilder.Entity<FinishedProduct>()
            .HasIndex(fp => fp.CategoryId);

        modelBuilder.Entity<FinishedProduct>()
            .HasIndex(fp => fp.IsActive);

        // Inventory Movement indexes
        modelBuilder.Entity<InventoryMovement>()
            .HasIndex(im => new { im.ItemType, im.ItemId });

        modelBuilder.Entity<InventoryMovement>()
            .HasIndex(im => im.MovementDate);

        modelBuilder.Entity<InventoryMovement>()
            .HasIndex(im => new { im.ReferenceType, im.ReferenceId });

        // Financial Transaction indexes
        modelBuilder.Entity<FinancialTransaction>()
            .HasIndex(ft => ft.TransactionDate);

        modelBuilder.Entity<FinancialTransaction>()
            .HasIndex(ft => new { ft.EntityType, ft.EntityId });

        modelBuilder.Entity<FinancialTransaction>()
            .HasIndex(ft => ft.TransactionType);

        modelBuilder.Entity<FinancialTransaction>()
            .HasIndex(ft => ft.Status);

        // Customer indexes
        modelBuilder.Entity<Customer>()
            .HasIndex(c => c.Name);

        // Supplier indexes
        modelBuilder.Entity<Supplier>()
            .HasIndex(s => s.Name);

        // Audit Log indexes
        modelBuilder.Entity<AuditLog>()
            .HasIndex(al => new { al.TableName, al.RecordId });

        modelBuilder.Entity<AuditLog>()
            .HasIndex(al => new { al.UserId, al.Timestamp });
    }

    private static void ConfigureCheckConstraints(ModelBuilder modelBuilder)
    {
        // Capital Transaction constraints
        modelBuilder.Entity<CapitalTransaction>()
            .HasCheckConstraint("CK_CapitalTransactions_Type", 
                "[TransactionType] IN ('Injection', 'Withdrawal')");

        modelBuilder.Entity<CapitalTransaction>()
            .HasCheckConstraint("CK_CapitalTransactions_Amount", 
                "[Amount] > 0");

        // Profit Distribution constraints
        modelBuilder.Entity<ProfitDistribution>()
            .HasCheckConstraint("CK_ProfitDistributions_Status", 
                "[Status] IN ('Calculated', 'Distributed', 'Cancelled')");

        // Category constraints
        modelBuilder.Entity<Category>()
            .HasCheckConstraint("CK_Categories_Type", 
                "[CategoryType] IN ('RawMaterial', 'FinishedProduct')");

        // Measurement Unit constraints
        modelBuilder.Entity<MeasurementUnit>()
            .HasCheckConstraint("CK_MeasurementUnits_Type", 
                "[UnitType] IN ('Weight', 'Count', 'Length', 'Volume', 'Area')");

        // Storage Location constraints
        modelBuilder.Entity<StorageLocation>()
            .HasCheckConstraint("CK_StorageLocations_Type", 
                "[LocationType] IN ('Warehouse', 'Section', 'Shelf', 'Bin')");

        // Inventory Movement constraints
        modelBuilder.Entity<InventoryMovement>()
            .HasCheckConstraint("CK_InventoryMovements_Type", 
                "[MovementType] IN ('Inbound', 'Outbound', 'Transfer', 'Adjustment')");

        modelBuilder.Entity<InventoryMovement>()
            .HasCheckConstraint("CK_InventoryMovements_ItemType", 
                "[ItemType] IN ('RawMaterial', 'FinishedProduct')");

        // Customer constraints
        modelBuilder.Entity<Customer>()
            .HasCheckConstraint("CK_Customers_Type", 
                "[CustomerType] IN ('Retail', 'Wholesale', 'Distributor')");

        // Supplier constraints
        modelBuilder.Entity<Supplier>()
            .HasCheckConstraint("CK_Suppliers_Type", 
                "[SupplierType] IN ('Material', 'Service', 'Equipment')");

        // Financial Account constraints
        modelBuilder.Entity<FinancialAccount>()
            .HasCheckConstraint("CK_FinancialAccounts_Type", 
                "[AccountType] IN ('Asset', 'Liability', 'Equity', 'Revenue', 'Expense')");

        // Financial Transaction constraints
        modelBuilder.Entity<FinancialTransaction>()
            .HasCheckConstraint("CK_FinancialTransactions_Status", 
                "[Status] IN ('Pending', 'Approved', 'Cancelled')");

        // Financial Transaction Detail constraints
        modelBuilder.Entity<FinancialTransactionDetail>()
            .HasCheckConstraint("CK_FinancialTransactionDetails_Amount", 
                "([DebitAmount] > 0 AND [CreditAmount] = 0) OR ([CreditAmount] > 0 AND [DebitAmount] = 0)");

        // System Setting constraints
        modelBuilder.Entity<SystemSetting>()
            .HasCheckConstraint("CK_SystemSettings_Type", 
                "[SettingType] IN ('String', 'Number', 'Boolean', 'Date')");

        // Audit Log constraints
        modelBuilder.Entity<AuditLog>()
            .HasCheckConstraint("CK_AuditLogs_Action", 
                "[Action] IN ('Insert', 'Update', 'Delete')");
    }

    public override async Task<int> SaveChangesAsync(CancellationToken cancellationToken = default)
    {
        // Update audit fields before saving
        UpdateAuditFields();
        
        return await base.SaveChangesAsync(cancellationToken);
    }

    private void UpdateAuditFields()
    {
        var entries = ChangeTracker.Entries<BaseEntity>();

        foreach (var entry in entries)
        {
            switch (entry.State)
            {
                case EntityState.Added:
                    entry.Entity.CreatedDate = DateTime.UtcNow;
                    break;
                case EntityState.Modified:
                    entry.Entity.ModifiedDate = DateTime.UtcNow;
                    break;
            }
        }
    }
}
