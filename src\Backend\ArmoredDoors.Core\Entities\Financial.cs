using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace ArmoredDoors.Core.Entities;

public class Customer : BaseActiveEntity
{
    [Required]
    [StringLength(20)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(100)]
    public string? ContactPerson { get; set; }

    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [StringLength(100)]
    public string? City { get; set; }

    [StringLength(100)]
    public string? Country { get; set; }

    [StringLength(20)]
    public string? TaxNumber { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal CreditLimit { get; set; } = 0;

    public int PaymentTerms { get; set; } = 30; // Days

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentBalance { get; set; } = 0;

    [Required]
    [StringLength(20)]
    public string CustomerType { get; set; } = "Retail"; // 'Retail', 'Wholesale', 'Distributor'

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation properties
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
}

public class Supplier : BaseActiveEntity
{
    [Required]
    [StringLength(20)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string Name { get; set; } = string.Empty;

    [StringLength(100)]
    public string? ContactPerson { get; set; }

    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    [StringLength(500)]
    public string? Address { get; set; }

    [StringLength(100)]
    public string? City { get; set; }

    [StringLength(100)]
    public string? Country { get; set; }

    [StringLength(20)]
    public string? TaxNumber { get; set; }

    public int PaymentTerms { get; set; } = 30; // Days

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentBalance { get; set; } = 0;

    [Required]
    [StringLength(20)]
    public string SupplierType { get; set; } = "Material"; // 'Material', 'Service', 'Equipment'

    [StringLength(1000)]
    public string? Notes { get; set; }

    // Navigation properties
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
}

public class Department : BaseActiveEntity
{
    [Required]
    [StringLength(20)]
    public string Code { get; set; } = string.Empty;

    [Required]
    [StringLength(100)]
    public string Name { get; set; } = string.Empty;

    [StringLength(255)]
    public string? Description { get; set; }

    public Guid? ManagerId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? BudgetLimit { get; set; }

    // Navigation properties
    public virtual User? Manager { get; set; }
    public virtual ICollection<Employee> Employees { get; set; } = new List<Employee>();
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();
}

public class Employee : BaseActiveEntity
{
    [Required]
    [StringLength(20)]
    public string EmployeeNumber { get; set; } = string.Empty;

    public Guid? UserId { get; set; }

    [Required]
    [StringLength(50)]
    public string FirstName { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string LastName { get; set; } = string.Empty;

    [StringLength(100)]
    [EmailAddress]
    public string? Email { get; set; }

    [StringLength(20)]
    public string? PhoneNumber { get; set; }

    public Guid? DepartmentId { get; set; }

    [StringLength(100)]
    public string? Position { get; set; }

    public DateTime HireDate { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal? Salary { get; set; }

    // Navigation properties
    public virtual User? User { get; set; }
    public virtual Department? Department { get; set; }
    public virtual ICollection<FinancialTransaction> FinancialTransactions { get; set; } = new List<FinancialTransaction>();

    // Computed property
    public string FullName => $"{FirstName} {LastName}";
}

public class FinancialAccount : BaseActiveEntity
{
    [Required]
    [StringLength(20)]
    public string AccountCode { get; set; } = string.Empty;

    [Required]
    [StringLength(200)]
    public string AccountName { get; set; } = string.Empty;

    [Required]
    [StringLength(50)]
    public string AccountType { get; set; } = string.Empty; // 'Asset', 'Liability', 'Equity', 'Revenue', 'Expense'

    public Guid? ParentAccountId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal CurrentBalance { get; set; } = 0;

    // Navigation properties
    public virtual FinancialAccount? ParentAccount { get; set; }
    public virtual ICollection<FinancialAccount> SubAccounts { get; set; } = new List<FinancialAccount>();
    public virtual ICollection<FinancialTransactionDetail> TransactionDetails { get; set; } = new List<FinancialTransactionDetail>();
}

public class FinancialTransaction : BaseEntity
{
    [Required]
    [StringLength(50)]
    public string TransactionNumber { get; set; } = string.Empty;

    public DateTime TransactionDate { get; set; }

    [Required]
    [StringLength(50)]
    public string TransactionType { get; set; } = string.Empty; // 'Sale', 'Purchase', 'Payment', 'Receipt', 'Expense', 'Revenue'

    [StringLength(20)]
    public string? EntityType { get; set; } // 'Customer', 'Supplier', 'Partner', 'Employee', 'Department'

    public Guid? EntityId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal TotalAmount { get; set; }

    [StringLength(500)]
    public string? Description { get; set; }

    [StringLength(50)]
    public string? ReferenceNumber { get; set; }

    [Required]
    [StringLength(20)]
    public string Status { get; set; } = "Pending"; // 'Pending', 'Approved', 'Cancelled'

    public Guid? ApprovedBy { get; set; }
    public DateTime? ApprovedDate { get; set; }

    // Navigation properties
    public virtual User? ApprovedByUser { get; set; }
    public virtual User CreatedByUser { get; set; } = null!;
    public virtual ICollection<FinancialTransactionDetail> TransactionDetails { get; set; } = new List<FinancialTransactionDetail>();

    // Polymorphic navigation properties
    public virtual Customer? Customer { get; set; }
    public virtual Supplier? Supplier { get; set; }
    public virtual Partner? Partner { get; set; }
    public virtual Employee? Employee { get; set; }
    public virtual Department? Department { get; set; }
}

public class FinancialTransactionDetail : BaseEntity
{
    public Guid TransactionId { get; set; }
    public Guid AccountId { get; set; }

    [Column(TypeName = "decimal(18,2)")]
    public decimal DebitAmount { get; set; } = 0;

    [Column(TypeName = "decimal(18,2)")]
    public decimal CreditAmount { get; set; } = 0;

    [StringLength(255)]
    public string? Description { get; set; }

    // Navigation properties
    public virtual FinancialTransaction Transaction { get; set; } = null!;
    public virtual FinancialAccount Account { get; set; } = null!;
}
