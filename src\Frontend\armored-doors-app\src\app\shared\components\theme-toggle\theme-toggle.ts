import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Subject } from 'rxjs';
import { takeUntil } from 'rxjs/operators';
import { ThemeService, ThemeState, ThemeMode } from '../../../core/services/theme';
import { LanguageService } from '../../../core/services/language';

@Component({
  selector: 'app-theme-toggle',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './theme-toggle.html',
  styleUrl: './theme-toggle.scss'
})
export class ThemeToggleComponent implements OnInit, OnDestroy {
  currentTheme: ThemeState = { mode: 'light', isDark: false, isAuto: false };
  isDropdownOpen = false;

  private destroy$ = new Subject<void>();

  constructor(
    private themeService: ThemeService,
    private languageService: LanguageService
  ) {}

  ngOnInit(): void {
    this.themeService.themeState$
      .pipe(takeUntil(this.destroy$))
      .subscribe(theme => {
        this.currentTheme = theme;
      });
  }

  ngOnDestroy(): void {
    this.destroy$.next();
    this.destroy$.complete();
  }

  toggleDropdown(): void {
    this.isDropdownOpen = !this.isDropdownOpen;
  }

  closeDropdown(): void {
    this.isDropdownOpen = false;
  }

  setTheme(mode: ThemeMode): void {
    this.themeService.setTheme(mode);
    this.closeDropdown();
  }

  quickToggle(): void {
    this.themeService.toggleTheme();
  }

  onKeyDown(event: KeyboardEvent): void {
    if (event.key === 'Escape') {
      this.closeDropdown();
    }
  }

  get isRTL(): boolean {
    return this.languageService.isRTL();
  }

  get currentLanguage() {
    return this.languageService.getCurrentLanguage();
  }

  getThemeIcon(mode: ThemeMode): string {
    switch (mode) {
      case 'light':
        return 'M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z';
      case 'dark':
        return 'M20.354 15.354A9 9 0 018.646 3.646 9.003 9.003 0 0012 21a9.003 9.003 0 008.354-5.646z';
      case 'auto':
        return 'M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z';
      default:
        return '';
    }
  }

  getThemeLabel(mode: ThemeMode): string {
    const isArabic = this.isRTL;

    switch (mode) {
      case 'light':
        return isArabic ? 'فاتح' : 'Light';
      case 'dark':
        return isArabic ? 'داكن' : 'Dark';
      case 'auto':
        return isArabic ? 'تلقائي' : 'Auto';
      default:
        return '';
    }
  }
}
