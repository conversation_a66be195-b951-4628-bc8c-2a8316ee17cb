{"version": 3, "file": "ro-MD.js", "sourceRoot": "", "sources": ["ro-MD.ts"], "names": [], "mappings": "AAAA;;;;;;GAMG;AAEH,0CAA0C;AAC1C,MAAM,CAAC,GAAG,SAAS,CAAC;AAEpB,eAAe,CAAC,CAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,OAAO,EAAC,QAAQ,CAAC,EAAC,CAAC,eAAe,EAAC,QAAQ,EAAC,WAAW,EAAC,aAAa,EAAC,OAAO,EAAC,SAAS,CAAC,EAAC,CAAC,CAAC,EAAC,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,EAAC,CAAC,OAAO,EAAC,OAAO,CAAC,CAAC,CAAC,CAAC", "sourcesContent": ["/**\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.dev/license\n */\n\n// THIS CODE IS GENERATED - DO NOT MODIFY.\nconst u = undefined;\n\nexport default [[[\"miezul nopții\",\"amiaz<PERSON>\",\"diminea<PERSON><PERSON>\",\"după-amiază\",\"seară\",\"noapte\"],[\"miezul nopții\",\"amiaz<PERSON>\",\"dimineața\",\"după-amiaza\",\"seara\",\"noaptea\"],u],u,[\"00:00\",\"12:00\",[\"05:00\",\"12:00\"],[\"12:00\",\"18:00\"],[\"18:00\",\"22:00\"],[\"22:00\",\"05:00\"]]];\n"]}